# EdgeOne UFW 故障排除指南

## 常见问题及解决方案

### 1. 无法正常添加到UFW

#### 问题症状
- 脚本运行但UFW规则没有添加
- 看到 "WARNING: 无法添加规则" 消息
- UFW状态中没有EdgeOne规则

#### 可能原因及解决方案

##### 原因1: UFW未启用
```bash
# 检查UFW状态
sudo ufw status

# 如果显示 "Status: inactive"，启用UFW
sudo ufw enable

# 重新运行EdgeOne脚本
sudo ./update_edgeone_ufw.sh --dry-run
```

##### 原因2: 权限问题
```bash
# 确保使用sudo运行
sudo ./update_edgeone_ufw.sh -v

# 检查脚本权限
ls -la update_edgeone_ufw.sh
chmod +x update_edgeone_ufw.sh
```

##### 原因3: UFW配置问题
```bash
# 重新加载UFW配置
sudo ufw --force reload

# 重置UFW（谨慎使用）
sudo ufw --force reset
sudo ufw enable
```

##### 原因4: 系统资源不足
```bash
# 检查内存使用
free -h

# 检查磁盘空间
df -h

# 检查UFW规则数量
sudo ufw status numbered | wc -l
```

### 2. 快速诊断步骤

#### 步骤1: 运行诊断脚本
```bash
sudo ./diagnose_ufw.sh
```

#### 步骤2: 简单测试
```bash
sudo ./test_ufw_simple.sh
```

#### 步骤3: 手动测试UFW
```bash
# 测试添加单个规则
sudo ufw allow from 192.168.1.100 to any port 53717 comment 'Test-Rule'

# 检查规则是否添加成功
sudo ufw status | grep 192.168.1.100

# 删除测试规则
sudo ufw status numbered | grep 192.168.1.100
sudo ufw delete [规则编号]
```

### 3. 详细调试

#### 启用详细输出
```bash
sudo ./update_edgeone_ufw.sh -v --dry-run
```

#### 检查日志
```bash
# 查看脚本日志
sudo tail -f /var/log/edgeone_ufw_update.log

# 查看系统UFW日志
sudo tail -f /var/log/ufw.log

# 查看系统日志
sudo journalctl -u ufw -f
```

### 4. 特定错误解决方案

#### 错误: "UFW command not found"
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install ufw

# CentOS/RHEL
sudo yum install ufw
# 或
sudo dnf install ufw
```

#### 错误: "Permission denied"
```bash
# 检查当前用户
whoami

# 确保使用sudo
sudo ./update_edgeone_ufw.sh

# 检查sudoers配置
sudo visudo
```

#### 错误: "Invalid rule"
```bash
# 检查IP格式
echo "*******/24" | grep -E '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/[0-9]+$'

# 检查端口格式
echo "53717" | grep -E '^[0-9]+$'

# 手动测试规则格式
sudo ufw allow from *******/24 to any port 53717
```

#### 错误: "Too many rules"
```bash
# 检查当前规则数量
sudo ufw status numbered | wc -l

# 清理旧的EdgeOne规则
sudo ./update_edgeone_ufw.sh --remove-all

# 重新添加规则
sudo ./update_edgeone_ufw.sh
```

### 5. 网络相关问题

#### API连接失败
```bash
# 测试API连接
curl -s https://api.edgeone.ai/ips | head -5

# 检查DNS解析
nslookup api.edgeone.ai

# 检查防火墙出站规则
sudo ufw status | grep OUT

# 测试HTTPS连接
wget --spider https://api.edgeone.ai/ips
```

#### IPv6支持问题
```bash
# 检查系统IPv6支持
cat /proc/net/if_inet6

# 检查UFW IPv6配置
sudo grep IPV6 /etc/default/ufw

# 启用IPv6支持
sudo sed -i 's/IPV6=no/IPV6=yes/' /etc/default/ufw
sudo ufw --force reload
```

### 6. 性能优化

#### 减少规则数量
```bash
# 使用特定端口而不是默认的多个端口
sudo ./update_edgeone_ufw.sh -p "53717"

# 定期清理不需要的规则
sudo ./update_edgeone_ufw.sh --remove-all
```

#### 批量处理
```bash
# 分批处理大量IP（如果脚本支持）
sudo ./update_edgeone_ufw.sh --batch-size 100
```

### 7. 系统兼容性

#### Ubuntu/Debian系统
```bash
# 更新系统
sudo apt update && sudo apt upgrade

# 安装必要依赖
sudo apt install curl ufw

# 检查UFW版本
ufw --version
```

#### CentOS/RHEL系统
```bash
# 安装EPEL仓库（如果需要）
sudo yum install epel-release

# 安装UFW
sudo yum install ufw

# 启动UFW服务
sudo systemctl enable ufw
sudo systemctl start ufw
```

### 8. 恢复和回滚

#### 备份当前UFW配置
```bash
# 备份UFW规则
sudo cp /etc/ufw/user.rules /etc/ufw/user.rules.backup
sudo cp /etc/ufw/user6.rules /etc/ufw/user6.rules.backup

# 导出当前规则
sudo ufw status numbered > ufw_rules_backup.txt
```

#### 删除所有EdgeOne规则
```bash
sudo ./update_edgeone_ufw.sh --remove-all
```

#### 重置UFW（谨慎使用）
```bash
# 完全重置UFW
sudo ufw --force reset

# 重新启用UFW
sudo ufw enable

# 恢复基本规则
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
```

### 9. 监控和维护

#### 设置监控
```bash
# 创建监控脚本
cat > /usr/local/bin/check_edgeone_rules.sh << 'EOF'
#!/bin/bash
RULE_COUNT=$(ufw status | grep -c EdgeOne || echo 0)
if [ $RULE_COUNT -eq 0 ]; then
    echo "警告: 没有EdgeOne规则" | logger -t edgeone-monitor
fi
EOF

chmod +x /usr/local/bin/check_edgeone_rules.sh

# 添加到crontab
echo "0 */6 * * * /usr/local/bin/check_edgeone_rules.sh" | sudo crontab -
```

#### 定期维护
```bash
# 每周检查规则状态
sudo ./update_edgeone_ufw.sh --dry-run -v

# 每月清理和重建规则
sudo ./update_edgeone_ufw.sh --remove-all
sudo ./update_edgeone_ufw.sh -v
```

### 10. 获取帮助

#### 收集诊断信息
```bash
# 生成完整诊断报告
sudo ./diagnose_ufw.sh > edgeone_diagnosis.txt 2>&1

# 收集系统信息
uname -a > system_info.txt
sudo ufw status verbose >> system_info.txt
sudo ufw --version >> system_info.txt
```

#### 联系支持
如果问题仍然存在，请提供以下信息：
1. 操作系统版本和架构
2. UFW版本
3. 错误消息的完整输出
4. 诊断脚本的输出
5. 相关日志文件内容

#### 社区资源
- UFW官方文档
- EdgeOne官方文档
- 相关GitHub Issues
- 系统管理员论坛

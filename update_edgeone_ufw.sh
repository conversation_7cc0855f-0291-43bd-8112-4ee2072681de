#!/bin/bash

# EdgeOne UFW Rules Update Script
# 自动获取腾讯云EdgeOne IP列表并更新UFW防火墙规则
# 支持IPv4和IPv6，智能对比增删规则

set -euo pipefail

# 配置变量
API_URL="https://api.edgeone.ai/ips"
TEMP_DIR="/tmp/edgeone_ufw"
CURRENT_IPS_FILE="$TEMP_DIR/current_ips.txt"
PREVIOUS_IPS_FILE="$TEMP_DIR/previous_ips.txt"
LOG_FILE="/var/log/edgeone_ufw_update.log"
RULE_PREFIX="EdgeOne"

# 默认端口配置 - 可根据需要修改
DEFAULT_PORTS="53717"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理
error_exit() {
    log "ERROR: $1"
    exit 1
}

# 显示帮助信息
show_help() {
    cat << EOF
EdgeOne UFW Rules Update Script

用法: $0 [选项]

选项:
    -p, --ports PORTS       指定端口列表，用逗号分隔 (默认: $DEFAULT_PORTS)
    -i, --interactive      交互式模式，提示用户输入端口
    -d, --dry-run          仅显示将要执行的操作，不实际修改防火墙规则
    -v, --verbose          详细输出模式
    -h, --help             显示此帮助信息
    --remove-all           删除所有EdgeOne相关的UFW规则

示例:
    $0                                    # 使用默认端口更新规则
    $0 -p "80,443,22"                    # 指定端口更新规则
    $0 -i                                # 交互式输入端口
    $0 --dry-run                         # 预览模式（推荐首次使用）
    $0 -i --dry-run                      # 交互式预览模式
    $0 --remove-all                      # 删除所有EdgeOne规则
    $0 start                             # 兼容定时任务调用

注意:
    - 首次使用建议先运行 --dry-run 预览模式
    - 脚本需要root权限来修改UFW规则
    - 当前默认端口: $DEFAULT_PORTS

EOF
}

# 解析命令行参数
PORTS="$DEFAULT_PORTS"
DRY_RUN=false
VERBOSE=false
REMOVE_ALL=false
INTERACTIVE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--ports)
            PORTS="$2"
            shift 2
            ;;
        -i|--interactive)
            INTERACTIVE=true
            shift
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        --remove-all)
            REMOVE_ALL=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        start|run|execute)
            # 兼容一些定时任务系统可能传入的参数
            log "检测到启动参数 '$1'，继续执行..."
            shift
            ;;
        *)
            log "WARNING: 未知参数: $1，将被忽略"
            shift
            ;;
    esac
done

# 检查权限和UFW状态
check_permissions() {
    if [[ $EUID -ne 0 ]] && [[ "$DRY_RUN" == false ]]; then
        error_exit "此脚本需要root权限来修改UFW规则。请使用sudo运行。"
    fi

    # 检查UFW是否安装
    if ! command -v ufw &> /dev/null; then
        error_exit "UFW未安装。请先安装UFW: sudo apt install ufw"
    fi

    # 检查UFW状态
    check_ufw_status
}

# 检查UFW状态和配置
check_ufw_status() {
    log "检查UFW状态..."

    local ufw_status=$(ufw status 2>&1)
    log "UFW状态: $ufw_status"

    # 检查UFW是否启用
    if echo "$ufw_status" | grep -q "Status: inactive"; then
        log "WARNING: UFW当前未启用"
        if [[ "$DRY_RUN" == false ]]; then
            log "正在启用UFW..."
            if ufw --force enable &>/dev/null; then
                log "UFW已成功启用"
            else
                error_exit "无法启用UFW，请手动检查"
            fi
        else
            log "DRY-RUN: 将启用UFW"
        fi
    elif echo "$ufw_status" | grep -q "Status: active"; then
        log "UFW已启用"
    else
        log "WARNING: 无法确定UFW状态: $ufw_status"
    fi

    # 检查现有EdgeOne规则数量
    local existing_rules=$(ufw status numbered | grep -c "$RULE_PREFIX" 2>/dev/null || echo 0)
    log "现有EdgeOne规则数量: $existing_rules"

    # 检查UFW配置
    if [[ "$VERBOSE" == true ]]; then
        log "UFW版本: $(ufw --version 2>/dev/null | head -1 || echo '未知')"
        log "UFW默认策略: $(ufw status verbose 2>/dev/null | grep -E 'Default:' || echo '未知')"
    fi
}

# 创建临时目录
setup_temp_dir() {
    mkdir -p "$TEMP_DIR"
    
    # 如果存在之前的IP列表，备份为previous
    if [[ -f "$CURRENT_IPS_FILE" ]]; then
        cp "$CURRENT_IPS_FILE" "$PREVIOUS_IPS_FILE"
    fi
}

# 获取EdgeOne IP列表
fetch_edgeone_ips() {
    log "正在获取EdgeOne IP列表..."
    
    if ! curl -s --connect-timeout 30 --max-time 60 "$API_URL" > "$CURRENT_IPS_FILE"; then
        error_exit "无法获取EdgeOne IP列表"
    fi
    
    # 验证获取的数据
    if [[ ! -s "$CURRENT_IPS_FILE" ]]; then
        error_exit "获取的IP列表为空"
    fi
    
    local ip_count=$(wc -l < "$CURRENT_IPS_FILE")
    log "成功获取 $ip_count 个IP段"
    
    if [[ "$VERBOSE" == true ]]; then
        log "IP列表预览（前10行）:"
        head -10 "$CURRENT_IPS_FILE" | while read line; do
            log "  $line"
        done
    fi
}

# 交互式输入端口
interactive_port_input() {
    echo
    echo "=== 交互式端口配置 ==="
    echo "当前默认端口: $DEFAULT_PORTS"
    echo
    echo "常用端口参考:"
    echo "  Web服务: 80,443"
    echo "  SSH: 22"
    echo "  FTP: 21,22"
    echo "  邮件: 25,110,143,993,995"
    echo "  数据库: 3306,5432,1433,27017"
    echo "  自定义Web: 8080,8443,9000"
    echo

    while true; do
        read -p "请输入要配置的端口列表（用逗号分隔，回车使用默认）: " user_ports

        # 如果用户直接回车，使用默认端口
        if [[ -z "$user_ports" ]]; then
            PORTS="$DEFAULT_PORTS"
            echo "使用默认端口: $PORTS"
            break
        fi

        # 验证端口格式
        if validate_ports "$user_ports"; then
            PORTS="$user_ports"
            echo "已设置端口: $PORTS"
            break
        else
            echo "端口格式错误，请重新输入。格式示例: 80,443,22"
        fi
    done

    echo
    read -p "是否继续执行更新？(y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo "操作已取消"
        exit 0
    fi
}

# 验证端口格式
validate_ports() {
    local ports="$1"

    # 检查是否只包含数字、逗号和空格
    if [[ ! "$ports" =~ ^[0-9,[:space:]]+$ ]]; then
        return 1
    fi

    # 分割端口并验证每个端口
    IFS=',' read -ra PORT_ARRAY <<< "$ports"
    for port in "${PORT_ARRAY[@]}"; do
        port=$(echo "$port" | xargs)  # 去除空格

        # 检查端口范围 (1-65535)
        if [[ ! "$port" =~ ^[0-9]+$ ]] || [[ "$port" -lt 1 ]] || [[ "$port" -gt 65535 ]]; then
            echo "错误: 端口 '$port' 无效。端口必须在 1-65535 范围内。"
            return 1
        fi
    done

    return 0
}

# 获取现有的EdgeOne UFW规则
get_existing_rules() {
    ufw status numbered | grep "$RULE_PREFIX" | awk '{print $NF}' | sort -u || true
}

# 分离IPv4和IPv6地址
separate_ip_versions() {
    local input_file="$1"
    local ipv4_file="$TEMP_DIR/ipv4_list.txt"
    local ipv6_file="$TEMP_DIR/ipv6_list.txt"
    
    # 清空文件
    > "$ipv4_file"
    > "$ipv6_file"
    
    while IFS= read -r line; do
        if [[ "$line" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/ ]]; then
            echo "$line" >> "$ipv4_file"
        elif [[ "$line" =~ : ]]; then
            echo "$line" >> "$ipv6_file"
        fi
    done < "$input_file"
    
    echo "$ipv4_file $ipv6_file"
}

# 添加UFW规则
add_ufw_rule() {
    local ip_range="$1"
    local port="$2"
    local comment="$RULE_PREFIX-$(echo "$ip_range" | tr '/:' '-')"

    if [[ "$DRY_RUN" == true ]]; then
        echo "DRY-RUN: ufw allow from $ip_range to any port $port comment '$comment'"
        return
    fi

    # 检查UFW是否启用
    if ! ufw status | grep -q "Status: active"; then
        log "WARNING: UFW未启用，正在启用UFW..."
        if ! ufw --force enable &>/dev/null; then
            log "ERROR: 无法启用UFW"
            return 1
        fi
    fi

    # 尝试添加规则，显示详细错误信息
    local ufw_output
    if ufw_output=$(ufw allow from "$ip_range" to any port "$port" comment "$comment" 2>&1); then
        if [[ "$VERBOSE" == true ]]; then
            log "添加规则: $ip_range -> 端口 $port"
        fi
    else
        log "ERROR: 无法添加规则 $ip_range -> 端口 $port"
        log "UFW错误信息: $ufw_output"

        # 尝试不带注释添加规则
        if ufw allow from "$ip_range" to any port "$port" &>/dev/null; then
            log "WARNING: 规则添加成功但无注释: $ip_range -> 端口 $port"
        else
            log "ERROR: 规则添加完全失败: $ip_range -> 端口 $port"
            return 1
        fi
    fi
}

# 删除UFW规则
remove_ufw_rule() {
    local rule_number="$1"
    
    if [[ "$DRY_RUN" == true ]]; then
        echo "DRY-RUN: ufw --force delete $rule_number"
        return
    fi
    
    if ufw --force delete "$rule_number" &>/dev/null; then
        log "删除规则: #$rule_number"
    else
        log "WARNING: 无法删除规则 #$rule_number"
    fi
}

# 删除所有EdgeOne规则
remove_all_edgeone_rules() {
    log "正在删除所有EdgeOne相关的UFW规则..."
    
    # 获取所有包含EdgeOne的规则编号（倒序删除避免编号变化）
    local rule_numbers=$(ufw status numbered | grep "$RULE_PREFIX" | awk '{print $1}' | tr -d '[]' | sort -nr)
    
    if [[ -z "$rule_numbers" ]]; then
        log "没有找到EdgeOne相关的UFW规则"
        return
    fi
    
    for rule_num in $rule_numbers; do
        if [[ "$DRY_RUN" == true ]]; then
            echo "DRY-RUN: 将删除规则 #$rule_num"
        else
            remove_ufw_rule "$rule_num"
        fi
    done
    
    log "EdgeOne规则删除完成"
}

# 比较IP列表并更新规则
update_ufw_rules() {
    local current_file="$1"
    local previous_file="$2"
    
    # 分离IPv4和IPv6
    local files=($(separate_ip_versions "$current_file"))
    local ipv4_file="${files[0]}"
    local ipv6_file="${files[1]}"
    
    log "开始更新UFW规则..."
    log "IPv4地址数量: $(wc -l < "$ipv4_file")"
    log "IPv6地址数量: $(wc -l < "$ipv6_file")"
    
    # 如果存在之前的列表，进行对比
    if [[ -f "$previous_file" ]]; then
        local prev_files=($(separate_ip_versions "$previous_file"))
        local prev_ipv4_file="${prev_files[0]}"
        local prev_ipv6_file="${prev_files[1]}"
        
        # 找出需要删除的IP（在旧列表中但不在新列表中）
        local to_remove_ipv4="$TEMP_DIR/remove_ipv4.txt"
        local to_remove_ipv6="$TEMP_DIR/remove_ipv6.txt"
        
        comm -23 <(sort "$prev_ipv4_file") <(sort "$ipv4_file") > "$to_remove_ipv4"
        comm -23 <(sort "$prev_ipv6_file") <(sort "$ipv6_file") > "$to_remove_ipv6"
        
        # 删除过时的规则
        if [[ -s "$to_remove_ipv4" ]] || [[ -s "$to_remove_ipv6" ]]; then
            log "发现需要删除的过时IP规则"
            # 这里我们通过重新生成所有规则来处理，因为UFW不容易精确匹配删除
            remove_all_edgeone_rules
        fi
    fi
    
    # 添加新规则
    IFS=',' read -ra PORT_ARRAY <<< "$PORTS"

    local total_ips=0
    local successful_rules=0
    local failed_rules=0

    for ip_file in "$ipv4_file" "$ipv6_file"; do
        if [[ ! -s "$ip_file" ]]; then
            continue
        fi

        local ip_type="IPv4"
        [[ "$ip_file" == *"ipv6"* ]] && ip_type="IPv6"

        log "处理 $ip_type 地址文件: $ip_file"

        while IFS= read -r ip_range; do
            [[ -z "$ip_range" ]] && continue
            total_ips=$((total_ips + 1))

            for port in "${PORT_ARRAY[@]}"; do
                port=$(echo "$port" | xargs)  # 去除空格

                if add_ufw_rule "$ip_range" "$port"; then
                    successful_rules=$((successful_rules + 1))
                else
                    failed_rules=$((failed_rules + 1))

                    # 如果失败太多，提前退出
                    if [[ $failed_rules -gt 10 ]]; then
                        log "ERROR: 连续失败规则过多，停止添加规则"
                        log "建议运行诊断脚本: sudo ./diagnose_ufw.sh"
                        break 3
                    fi
                fi
            done
        done < "$ip_file"
    done

    log "UFW规则更新完成"
    log "统计: 总IP段=$total_ips, 成功规则=$successful_rules, 失败规则=$failed_rules"

    if [[ $failed_rules -gt 0 ]]; then
        log "WARNING: 有 $failed_rules 个规则添加失败，建议检查UFW状态"
    fi
}

# 主函数
main() {
    log "EdgeOne UFW更新脚本开始执行"

    # 检查权限和依赖
    check_permissions

    # 设置临时目录
    setup_temp_dir

    # 处理删除所有规则的情况
    if [[ "$REMOVE_ALL" == true ]]; then
        remove_all_edgeone_rules
        log "脚本执行完成"
        exit 0
    fi

    # 交互式端口输入
    if [[ "$INTERACTIVE" == true ]]; then
        interactive_port_input
    fi

    # 验证端口格式
    if ! validate_ports "$PORTS"; then
        error_exit "端口格式错误: $PORTS"
    fi

    log "使用端口: $PORTS"

    # 获取最新IP列表
    fetch_edgeone_ips

    # 更新UFW规则
    if [[ -f "$PREVIOUS_IPS_FILE" ]]; then
        update_ufw_rules "$CURRENT_IPS_FILE" "$PREVIOUS_IPS_FILE"
    else
        log "首次运行，添加所有规则"
        update_ufw_rules "$CURRENT_IPS_FILE" "/dev/null"
    fi

    # 显示当前状态
    if [[ "$VERBOSE" == true ]] || [[ "$DRY_RUN" == true ]]; then
        log "当前EdgeOne相关的UFW规则数量: $(ufw status | grep -c "$RULE_PREFIX" || echo 0)"
    fi

    log "脚本执行完成"
}

# 信号处理
trap 'log "脚本被中断"; exit 1' INT TERM

# 执行主函数
main "$@"

# EdgeOne UFW 交互式端口配置使用指南

## 概述

EdgeOne UFW更新器现在支持交互式端口配置，让您可以在运行时动态选择要配置的端口，而不需要在命令行中预先指定。

## 如何使用交互式模式

### 1. 基本交互式命令

```bash
# 交互式模式 + 预览（推荐首次使用）
sudo ./update_edgeone_ufw.sh -i --dry-run

# 交互式模式 + 实际执行
sudo ./update_edgeone_ufw.sh -i

# 交互式模式 + 详细输出
sudo ./update_edgeone_ufw.sh -i -v
```

### 2. 交互式流程示例

当您运行交互式命令时，会看到如下提示：

```
=== 交互式端口配置 ===
当前默认端口: 80,443,8080,8443

常用端口参考:
  Web服务: 80,443
  SSH: 22
  FTP: 21,22
  邮件: 25,110,143,993,995
  数据库: 3306,5432,1433,27017
  自定义Web: 8080,8443,9000

请输入要配置的端口列表（用逗号分隔，回车使用默认）: 
```

### 3. 端口输入选项

您可以选择以下任一方式：

#### 选项1: 使用默认端口
直接按回车键，使用默认端口 `80,443,8080,8443`

#### 选项2: 输入自定义端口
输入您需要的端口，例如：
- `80,443` - 仅Web服务端口
- `22,80,443` - SSH + Web服务端口  
- `80,443,22,3306` - Web + SSH + MySQL端口
- `8080,8443,9000` - 自定义Web服务端口

### 4. 完整交互示例

```bash
$ sudo ./update_edgeone_ufw.sh -i --dry-run

[2025-07-25 14:50:00] EdgeOne UFW更新脚本开始执行

=== 交互式端口配置 ===
当前默认端口: 80,443,8080,8443

常用端口参考:
  Web服务: 80,443
  SSH: 22
  FTP: 21,22
  邮件: 25,110,143,993,995
  数据库: 3306,5432,1433,27017
  自定义Web: 8080,8443,9000

请输入要配置的端口列表（用逗号分隔，回车使用默认）: 80,443,22
已设置端口: 80,443,22

是否继续执行更新？(y/N): y
[2025-07-25 14:50:15] 使用端口: 80,443,22
[2025-07-25 14:50:15] 正在获取EdgeOne IP列表...
[2025-07-25 14:50:16] 成功获取 500 个IP段
[2025-07-25 14:50:16] IPv4地址数量: 167
[2025-07-25 14:50:16] IPv6地址数量: 333
[2025-07-25 14:50:16] 开始更新UFW规则...
DRY-RUN: ufw allow from ***********/24 to any port 80 comment 'EdgeOne-***********-24'
DRY-RUN: ufw allow from ***********/24 to any port 443 comment 'EdgeOne-***********-24'
DRY-RUN: ufw allow from ***********/24 to any port 22 comment 'EdgeOne-***********-24'
...
```

## 端口验证

脚本会自动验证您输入的端口：

### 有效端口格式
- `80,443,22` ✓
- `8080, 8443, 9000` ✓ (允许空格)
- `1,65535` ✓ (端口范围1-65535)

### 无效端口格式
- `0,80` ✗ (端口0无效)
- `80,65536` ✗ (端口超出范围)
- `80,abc` ✗ (非数字)
- `80;443` ✗ (错误分隔符)

如果输入无效端口，脚本会提示重新输入。

## 常用端口参考

| 服务类型 | 端口 | 说明 |
|---------|------|------|
| Web服务 | 80,443 | HTTP和HTTPS |
| SSH | 22 | 安全Shell |
| FTP | 21,22 | 文件传输 |
| 邮件 | 25,110,143,993,995 | SMTP,POP3,IMAP |
| 数据库 | 3306,5432,1433,27017 | MySQL,PostgreSQL,SQL Server,MongoDB |
| 自定义Web | 8080,8443,9000 | 常用的替代Web端口 |

## 安全建议

1. **首次使用预览模式**: 使用 `--dry-run` 参数先预览将要创建的规则
2. **最小权限原则**: 只开放必要的端口
3. **定期审查**: 定期检查和清理不需要的规则
4. **日志监控**: 查看 `/var/log/edgeone_ufw_update.log` 了解更新历史

## 故障排除

### 问题1: 没有看到交互式提示
**原因**: 没有使用 `-i` 参数
**解决**: 确保命令中包含 `-i` 或 `--interactive` 参数

### 问题2: 端口验证失败
**原因**: 端口格式不正确
**解决**: 检查端口格式，确保使用逗号分隔，端口在1-65535范围内

### 问题3: 权限错误
**原因**: 没有使用sudo运行
**解决**: 使用 `sudo` 运行脚本

## 相关命令

```bash
# 查看当前EdgeOne规则
sudo ufw status | grep EdgeOne

# 删除所有EdgeOne规则
sudo ./update_edgeone_ufw.sh --remove-all

# 查看脚本帮助
./update_edgeone_ufw.sh --help

# 查看更新日志
sudo tail -f /var/log/edgeone_ufw_update.log
```

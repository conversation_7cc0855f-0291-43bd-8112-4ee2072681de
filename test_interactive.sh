#!/bin/bash

# 测试交互式功能的简单脚本

echo "测试EdgeOne UFW脚本的交互式功能"
echo "================================"

# 检查脚本是否存在
if [[ ! -f "update_edgeone_ufw.sh" ]]; then
    echo "错误: 找不到 update_edgeone_ufw.sh"
    exit 1
fi

echo "1. 测试帮助信息（包含交互式选项）:"
echo "----------------------------------------"
./update_edgeone_ufw.sh --help | grep -A 10 -B 5 "interactive"

echo
echo "2. 测试脚本语法:"
echo "----------------------------------------"
if bash -n update_edgeone_ufw.sh; then
    echo "✓ 脚本语法正确"
else
    echo "✗ 脚本语法错误"
    exit 1
fi

echo
echo "3. 模拟交互式输入测试:"
echo "----------------------------------------"

# 创建一个测试用的输入文件
cat > /tmp/test_input.txt << 'EOF'
80,443,22
y
EOF

echo "模拟用户输入: 80,443,22 然后确认"
echo "运行命令: sudo ./update_edgeone_ufw.sh -i --dry-run < /tmp/test_input.txt"
echo

if [[ $EUID -eq 0 ]]; then
    ./update_edgeone_ufw.sh -i --dry-run < /tmp/test_input.txt
else
    echo "需要sudo权限来运行完整测试，显示命令供手动执行:"
    echo "sudo ./update_edgeone_ufw.sh -i --dry-run"
fi

# 清理
rm -f /tmp/test_input.txt

echo
echo "4. 手动测试建议:"
echo "----------------------------------------"
echo "要手动测试交互式功能，请运行:"
echo "  sudo ./update_edgeone_ufw.sh -i --dry-run"
echo
echo "然后按提示输入端口，例如:"
echo "  - 直接回车使用默认端口"
echo "  - 输入 80,443 使用Web端口"
echo "  - 输入 22,80,443 包含SSH端口"

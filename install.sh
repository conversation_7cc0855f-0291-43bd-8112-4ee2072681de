#!/bin/bash

# EdgeOne UFW更新器安装脚本
# 自动安装和配置EdgeOne UFW规则更新服务

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 错误处理
error_exit() {
    log_error "$1"
    exit 1
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        error_exit "此安装脚本需要root权限。请使用sudo运行。"
    fi
}

# 检查系统要求
check_requirements() {
    log_step "检查系统要求..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        error_exit "无法确定操作系统类型"
    fi
    
    source /etc/os-release
    log_info "检测到操作系统: $PRETTY_NAME"
    
    # 检查必要命令
    local required_commands=("curl" "systemctl")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error_exit "缺少必要命令: $cmd"
        fi
    done
    
    # 检查UFW
    if ! command -v ufw &> /dev/null; then
        log_warn "UFW未安装，正在安装..."
        if command -v apt &> /dev/null; then
            apt update && apt install -y ufw
        elif command -v yum &> /dev/null; then
            yum install -y ufw
        elif command -v dnf &> /dev/null; then
            dnf install -y ufw
        else
            error_exit "无法自动安装UFW，请手动安装后重试"
        fi
    fi
    
    log_info "系统要求检查完成"
}

# 安装主脚本
install_main_script() {
    log_step "安装主脚本..."
    
    local script_path="/usr/local/bin/update_edgeone_ufw.sh"
    
    if [[ ! -f "update_edgeone_ufw.sh" ]]; then
        error_exit "找不到主脚本文件 update_edgeone_ufw.sh"
    fi
    
    cp "update_edgeone_ufw.sh" "$script_path"
    chmod +x "$script_path"
    
    log_info "主脚本已安装到: $script_path"
}

# 安装systemd服务
install_systemd_service() {
    log_step "安装systemd服务..."
    
    local service_file="/etc/systemd/system/edgeone-ufw-update.service"
    local timer_file="/etc/systemd/system/edgeone-ufw-update.timer"
    
    if [[ ! -f "edgeone-ufw-update.service" ]]; then
        error_exit "找不到服务文件 edgeone-ufw-update.service"
    fi
    
    if [[ ! -f "edgeone-ufw-update.timer" ]]; then
        error_exit "找不到定时器文件 edgeone-ufw-update.timer"
    fi
    
    cp "edgeone-ufw-update.service" "$service_file"
    cp "edgeone-ufw-update.timer" "$timer_file"
    
    # 重新加载systemd配置
    systemctl daemon-reload
    
    log_info "systemd服务文件已安装"
}

# 配置日志轮转
setup_log_rotation() {
    log_step "配置日志轮转..."
    
    cat > /etc/logrotate.d/edgeone-ufw-update << 'EOF'
/var/log/edgeone_ufw_update.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        systemctl reload-or-restart rsyslog > /dev/null 2>&1 || true
    endscript
}
EOF
    
    log_info "日志轮转配置完成"
}

# 创建配置文件
create_config() {
    log_step "创建配置文件..."
    
    local config_dir="/etc/edgeone-ufw-updater"
    local config_file="$config_dir/config.conf"
    
    mkdir -p "$config_dir"
    
    cat > "$config_file" << 'EOF'
# EdgeOne UFW更新器配置文件

# API URL
API_URL="https://api.edgeone.ai/ips"

# 默认端口列表（逗号分隔）
DEFAULT_PORTS="80,443,8080,8443"

# 日志文件路径
LOG_FILE="/var/log/edgeone_ufw_update.log"

# 临时目录
TEMP_DIR="/tmp/edgeone_ufw"

# UFW规则前缀
RULE_PREFIX="EdgeOne"

# 更新间隔（systemd timer格式）
# 默认每天凌晨2点执行
UPDATE_SCHEDULE="*-*-* 02:00:00"

# 随机延迟（秒）
RANDOM_DELAY=1800
EOF
    
    chmod 644 "$config_file"
    log_info "配置文件已创建: $config_file"
}

# 启用服务
enable_service() {
    log_step "启用和启动服务..."
    
    # 启用定时器
    systemctl enable edgeone-ufw-update.timer
    systemctl start edgeone-ufw-update.timer
    
    log_info "定时器已启用并启动"
    
    # 显示状态
    systemctl status edgeone-ufw-update.timer --no-pager -l
}

# 测试安装
test_installation() {
    log_step "测试安装..."
    
    # 测试脚本执行
    log_info "执行测试运行（dry-run模式）..."
    if /usr/local/bin/update_edgeone_ufw.sh --dry-run; then
        log_info "测试运行成功"
    else
        log_warn "测试运行失败，请检查配置"
    fi
    
    # 检查定时器状态
    if systemctl is-active --quiet edgeone-ufw-update.timer; then
        log_info "定时器运行正常"
    else
        log_warn "定时器未正常运行"
    fi
}

# 显示安装后信息
show_post_install_info() {
    echo
    log_info "EdgeOne UFW更新器安装完成！"
    echo
    echo "使用方法："
    echo "  手动更新规则:     sudo /usr/local/bin/update_edgeone_ufw.sh"
    echo "  预览模式:         sudo /usr/local/bin/update_edgeone_ufw.sh --dry-run"
    echo "  指定端口:         sudo /usr/local/bin/update_edgeone_ufw.sh -p '80,443,22'"
    echo "  删除所有规则:     sudo /usr/local/bin/update_edgeone_ufw.sh --remove-all"
    echo
    echo "服务管理："
    echo "  查看定时器状态:   sudo systemctl status edgeone-ufw-update.timer"
    echo "  查看服务日志:     sudo journalctl -u edgeone-ufw-update.service"
    echo "  查看更新日志:     sudo tail -f /var/log/edgeone_ufw_update.log"
    echo "  停用定时器:       sudo systemctl disable edgeone-ufw-update.timer"
    echo "  启用定时器:       sudo systemctl enable edgeone-ufw-update.timer"
    echo
    echo "配置文件："
    echo "  主配置:           /etc/edgeone-ufw-updater/config.conf"
    echo "  服务配置:         /etc/systemd/system/edgeone-ufw-update.service"
    echo "  定时器配置:       /etc/systemd/system/edgeone-ufw-update.timer"
    echo
    echo "下次自动更新时间："
    systemctl list-timers edgeone-ufw-update.timer --no-pager | tail -n +2
}

# 卸载函数
uninstall() {
    log_step "卸载EdgeOne UFW更新器..."
    
    # 停止并禁用服务
    systemctl stop edgeone-ufw-update.timer 2>/dev/null || true
    systemctl disable edgeone-ufw-update.timer 2>/dev/null || true
    
    # 删除文件
    rm -f /usr/local/bin/update_edgeone_ufw.sh
    rm -f /etc/systemd/system/edgeone-ufw-update.service
    rm -f /etc/systemd/system/edgeone-ufw-update.timer
    rm -f /etc/logrotate.d/edgeone-ufw-update
    rm -rf /etc/edgeone-ufw-updater
    
    # 重新加载systemd
    systemctl daemon-reload
    
    log_info "卸载完成"
}

# 显示帮助
show_help() {
    cat << EOF
EdgeOne UFW更新器安装脚本

用法: $0 [选项]

选项:
    install     安装EdgeOne UFW更新器（默认）
    uninstall   卸载EdgeOne UFW更新器
    test        测试当前安装
    -h, --help  显示此帮助信息

示例:
    $0                # 安装
    $0 install        # 安装
    $0 uninstall      # 卸载
    $0 test           # 测试

EOF
}

# 主函数
main() {
    local action="${1:-install}"
    
    case "$action" in
        install)
            check_permissions
            check_requirements
            install_main_script
            install_systemd_service
            setup_log_rotation
            create_config
            enable_service
            test_installation
            show_post_install_info
            ;;
        uninstall)
            check_permissions
            uninstall
            ;;
        test)
            check_permissions
            test_installation
            ;;
        -h|--help)
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

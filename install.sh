#!/bin/bash

# EdgeOne UFW更新器安装脚本
# 自动安装和配置EdgeOne UFW规则更新服务

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 错误处理
error_exit() {
    log_error "$1"
    exit 1
}

# 检查权限
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        error_exit "此安装脚本需要root权限。请使用sudo运行。"
    fi
}

# 选择防火墙类型
choose_firewall_type() {
    echo
    log_info "请选择要使用的防火墙类型:"
    echo "1) UFW (Uncomplicated Firewall) - 适合Ubuntu/Debian"
    echo "2) iptables - 适合所有Linux发行版"
    echo

    while true; do
        read -p "请选择 (1-2): " choice
        case $choice in
            1)
                FIREWALL_TYPE="ufw"
                SCRIPT_NAME="update_edgeone_ufw.sh"
                break
                ;;
            2)
                FIREWALL_TYPE="iptables"
                SCRIPT_NAME="update_edgeone_iptables.sh"
                break
                ;;
            *)
                echo "无效选择，请输入 1 或 2"
                ;;
        esac
    done

    log_info "已选择: $FIREWALL_TYPE"
}

# 检查系统要求
check_requirements() {
    log_step "检查系统要求..."

    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        error_exit "无法确定操作系统类型"
    fi

    source /etc/os-release
    log_info "检测到操作系统: $PRETTY_NAME"

    # 检查必要命令
    local required_commands=("curl" "systemctl")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error_exit "缺少必要命令: $cmd"
        fi
    done

    # 根据防火墙类型检查相应工具
    if [[ "$FIREWALL_TYPE" == "ufw" ]]; then
        check_ufw_installation
    elif [[ "$FIREWALL_TYPE" == "iptables" ]]; then
        check_iptables_installation
    fi

    log_info "系统要求检查完成"
}

# 检查UFW安装
check_ufw_installation() {
    if ! command -v ufw &> /dev/null; then
        log_warn "UFW未安装，正在安装..."
        if command -v apt &> /dev/null; then
            apt update && apt install -y ufw
        elif command -v yum &> /dev/null; then
            yum install -y ufw
        elif command -v dnf &> /dev/null; then
            dnf install -y ufw
        else
            error_exit "无法自动安装UFW，请手动安装后重试"
        fi
    fi
}

# 检查iptables安装
check_iptables_installation() {
    if ! command -v iptables &> /dev/null; then
        log_warn "iptables未安装，正在安装..."
        if command -v apt &> /dev/null; then
            apt update && apt install -y iptables iptables-persistent
        elif command -v yum &> /dev/null; then
            yum install -y iptables-services
            systemctl enable iptables
            systemctl enable ip6tables
        elif command -v dnf &> /dev/null; then
            dnf install -y iptables-services
            systemctl enable iptables
            systemctl enable ip6tables
        else
            error_exit "无法自动安装iptables，请手动安装后重试"
        fi
    fi

    # 检查iptables-save命令
    if ! command -v iptables-save &> /dev/null; then
        log_warn "iptables-save未找到，某些功能可能受限"
    fi
}

# 安装主脚本
install_main_script() {
    log_step "安装主脚本..."

    local script_path="/usr/local/bin/${SCRIPT_NAME}"

    if [[ ! -f "$SCRIPT_NAME" ]]; then
        error_exit "找不到主脚本文件 $SCRIPT_NAME"
    fi

    cp "$SCRIPT_NAME" "$script_path"
    chmod +x "$script_path"

    log_info "主脚本已安装到: $script_path"
}

# 安装systemd服务
install_systemd_service() {
    log_step "安装systemd服务..."

    local service_name="edgeone-${FIREWALL_TYPE}-update"
    local service_file="/etc/systemd/system/${service_name}.service"
    local timer_file="/etc/systemd/system/${service_name}.timer"
    local source_service="${service_name}.service"
    local source_timer="${service_name}.timer"

    if [[ ! -f "$source_service" ]]; then
        error_exit "找不到服务文件 $source_service"
    fi

    if [[ ! -f "$source_timer" ]]; then
        error_exit "找不到定时器文件 $source_timer"
    fi

    cp "$source_service" "$service_file"
    cp "$source_timer" "$timer_file"

    # 重新加载systemd配置
    systemctl daemon-reload

    log_info "systemd服务文件已安装: $service_name"

    # 保存服务名称供后续使用
    SERVICE_NAME="$service_name"
}

# 配置日志轮转
setup_log_rotation() {
    log_step "配置日志轮转..."

    local log_file="/var/log/edgeone_${FIREWALL_TYPE}_update.log"
    local logrotate_config="/etc/logrotate.d/edgeone-${FIREWALL_TYPE}-update"

    cat > "$logrotate_config" << EOF
$log_file {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        systemctl reload-or-restart rsyslog > /dev/null 2>&1 || true
    endscript
}
EOF

    log_info "日志轮转配置完成: $logrotate_config"
}

# 创建配置文件
create_config() {
    log_step "创建配置文件..."

    local config_dir="/etc/edgeone-${FIREWALL_TYPE}-updater"
    local config_file="$config_dir/config.conf"

    mkdir -p "$config_dir"

    cat > "$config_file" << EOF
# EdgeOne ${FIREWALL_TYPE^^} 更新器配置文件

# API URL
API_URL="https://api.edgeone.ai/ips"

# 默认端口列表（逗号分隔）
DEFAULT_PORTS="80,443,8080,8443"

# 日志文件路径
LOG_FILE="/var/log/edgeone_${FIREWALL_TYPE}_update.log"

# 临时目录
TEMP_DIR="/tmp/edgeone_${FIREWALL_TYPE}"

# 规则前缀/链名称
RULE_PREFIX="EdgeOne"
CHAIN_NAME="EDGEONE_ALLOW"

# 更新间隔（systemd timer格式）
# 默认每天凌晨2点执行
UPDATE_SCHEDULE="*-*-* 02:00:00"

# 随机延迟（秒）
RANDOM_DELAY=1800

# 防火墙类型
FIREWALL_TYPE="${FIREWALL_TYPE}"
EOF

    chmod 644 "$config_file"
    log_info "配置文件已创建: $config_file"
}

# 启用服务
enable_service() {
    log_step "启用和启动服务..."

    local timer_name="${SERVICE_NAME}.timer"

    # 启用定时器
    systemctl enable "$timer_name"
    systemctl start "$timer_name"

    log_info "定时器已启用并启动: $timer_name"

    # 显示状态
    systemctl status "$timer_name" --no-pager -l
}

# 测试安装
test_installation() {
    log_step "测试安装..."

    local script_path="/usr/local/bin/${SCRIPT_NAME}"
    local timer_name="${SERVICE_NAME}.timer"

    # 测试脚本执行
    log_info "执行测试运行（dry-run模式）..."
    if "$script_path" --dry-run; then
        log_info "测试运行成功"
    else
        log_warn "测试运行失败，请检查配置"
    fi

    # 检查定时器状态
    if systemctl is-active --quiet "$timer_name"; then
        log_info "定时器运行正常"
    else
        log_warn "定时器未正常运行"
    fi
}

# 显示安装后信息
show_post_install_info() {
    local script_path="/usr/local/bin/${SCRIPT_NAME}"
    local timer_name="${SERVICE_NAME}.timer"
    local service_name="${SERVICE_NAME}.service"
    local config_dir="/etc/edgeone-${FIREWALL_TYPE}-updater"
    local log_file="/var/log/edgeone_${FIREWALL_TYPE}_update.log"

    echo
    log_info "EdgeOne ${FIREWALL_TYPE^^} 更新器安装完成！"
    echo
    echo "使用方法："
    echo "  手动更新规则:     sudo $script_path"
    echo "  预览模式:         sudo $script_path --dry-run"
    echo "  指定端口:         sudo $script_path -p '53717'"
    echo "  交互式配置:       sudo $script_path -i"
    echo "  删除所有规则:     sudo $script_path --remove-all"
    echo
    echo "服务管理："
    echo "  查看定时器状态:   sudo systemctl status $timer_name"
    echo "  查看服务日志:     sudo journalctl -u $service_name"
    echo "  查看更新日志:     sudo tail -f $log_file"
    echo "  停用定时器:       sudo systemctl disable $timer_name"
    echo "  启用定时器:       sudo systemctl enable $timer_name"
    echo
    echo "配置文件："
    echo "  主配置:           $config_dir/config.conf"
    echo "  服务配置:         /etc/systemd/system/$service_name"
    echo "  定时器配置:       /etc/systemd/system/$timer_name"
    echo
    echo "测试命令："
    if [[ "$FIREWALL_TYPE" == "iptables" ]]; then
        echo "  测试环境:         sudo ./test_iptables.sh"
        echo "  查看规则:         sudo iptables -L EDGEONE_ALLOW -n"
    else
        echo "  测试环境:         sudo ./test_ufw_simple.sh"
        echo "  查看规则:         sudo ufw status | grep EdgeOne"
    fi
    echo
    echo "下次自动更新时间："
    systemctl list-timers "$timer_name" --no-pager | tail -n +2
}

# 卸载函数
uninstall() {
    log_step "卸载EdgeOne防火墙更新器..."

    # 尝试检测已安装的版本
    local installed_versions=()

    if [[ -f "/usr/local/bin/update_edgeone_ufw.sh" ]]; then
        installed_versions+=("ufw")
    fi

    if [[ -f "/usr/local/bin/update_edgeone_iptables.sh" ]]; then
        installed_versions+=("iptables")
    fi

    if [[ ${#installed_versions[@]} -eq 0 ]]; then
        log_info "未发现已安装的EdgeOne更新器"
        return
    fi

    # 卸载所有发现的版本
    for fw_type in "${installed_versions[@]}"; do
        log_info "卸载 $fw_type 版本..."

        local service_name="edgeone-${fw_type}-update"

        # 停止并禁用服务
        systemctl stop "${service_name}.timer" 2>/dev/null || true
        systemctl disable "${service_name}.timer" 2>/dev/null || true

        # 删除文件
        rm -f "/usr/local/bin/update_edgeone_${fw_type}.sh"
        rm -f "/etc/systemd/system/${service_name}.service"
        rm -f "/etc/systemd/system/${service_name}.timer"
        rm -f "/etc/logrotate.d/${service_name}"
        rm -rf "/etc/edgeone-${fw_type}-updater"

        log_info "$fw_type 版本卸载完成"
    done

    # 重新加载systemd
    systemctl daemon-reload

    log_info "所有版本卸载完成"
}

# 显示帮助
show_help() {
    cat << EOF
EdgeOne 防火墙更新器安装脚本

用法: $0 [选项]

选项:
    install             安装EdgeOne防火墙更新器（默认）
    install-ufw         安装UFW版本
    install-iptables    安装iptables版本
    uninstall           卸载EdgeOne防火墙更新器
    test                测试当前安装
    -h, --help          显示此帮助信息

示例:
    $0                      # 安装（会提示选择防火墙类型）
    $0 install-ufw          # 安装UFW版本
    $0 install-iptables     # 安装iptables版本
    $0 uninstall            # 卸载
    $0 test                 # 测试

EOF
}

# 主函数
main() {
    local action="${1:-install}"

    # 全局变量
    FIREWALL_TYPE=""
    SCRIPT_NAME=""

    case "$action" in
        install)
            check_permissions
            choose_firewall_type
            check_requirements
            install_main_script
            install_systemd_service
            setup_log_rotation
            create_config
            enable_service
            test_installation
            show_post_install_info
            ;;
        install-ufw)
            check_permissions
            FIREWALL_TYPE="ufw"
            SCRIPT_NAME="update_edgeone_ufw.sh"
            check_requirements
            install_main_script
            install_systemd_service
            setup_log_rotation
            create_config
            enable_service
            test_installation
            show_post_install_info
            ;;
        install-iptables)
            check_permissions
            FIREWALL_TYPE="iptables"
            SCRIPT_NAME="update_edgeone_iptables.sh"
            check_requirements
            install_main_script
            install_systemd_service
            setup_log_rotation
            
            enable_service
            test_installation
            show_post_install_info
            ;;
        uninstall)
            check_permissions
            uninstall
            ;;
        test)
            check_permissions
            test_installation
            ;;
        -h|--help)
            show_help
            ;;
        *)
            log_error "未知操作: $action"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

# EdgeOne 防火墙更新器完整使用指南

## 概述

EdgeOne 防火墙更新器是一个自动化工具，用于获取腾讯云 EdgeOne 的 IP 地址列表并更新防火墙规则。支持 UFW 和 iptables 两种防火墙类型。

## 安装指南

### 1. 下载项目文件

```bash
# 下载所有必要文件到服务器
# 确保以下文件存在：
# - install.sh
# - update_edgeone_ufw.sh (UFW版本)
# - update_edgeone_iptables.sh (iptables版本)
# - edgeone-ufw-update.service/timer (UFW systemd文件)
# - edgeone-iptables-update.service/timer (iptables systemd文件)
```

### 2. 选择安装方式

#### 方式1：交互式安装（推荐）
```bash
chmod +x install.sh
sudo ./install.sh
# 会提示选择防火墙类型
```

#### 方式2：直接安装指定类型
```bash
# 安装UFW版本
sudo ./install.sh install-ufw

# 安装iptables版本
sudo ./install.sh install-iptables
```

### 3. 验证安装

```bash
# 运行测试脚本
sudo ./test.sh

# 或者手动测试
sudo /usr/local/bin/update_edgeone_ufw.sh --dry-run      # UFW版本
sudo /usr/local/bin/update_edgeone_iptables.sh --dry-run # iptables版本
```

## 基本使用

### UFW 版本

```bash
# 基本命令
sudo update_edgeone_ufw.sh                    # 使用默认端口
sudo update_edgeone_ufw.sh -p "53717"         # 指定端口
sudo update_edgeone_ufw.sh -i                 # 交互式配置
sudo update_edgeone_ufw.sh --dry-run          # 预览模式
sudo update_edgeone_ufw.sh --remove-all       # 删除所有规则

# 查看当前规则
sudo ufw status | grep EdgeOne
```

### iptables 版本

```bash
# 基本命令
sudo update_edgeone_iptables.sh               # 使用默认端口
sudo update_edgeone_iptables.sh -p "53717"    # 指定端口
sudo update_edgeone_iptables.sh -i            # 交互式配置
sudo update_edgeone_iptables.sh --dry-run     # 预览模式
sudo update_edgeone_iptables.sh --remove-all  # 删除所有规则

# 查看当前规则
sudo iptables -L EDGEONE_ALLOW -n             # IPv4规则
sudo ip6tables -L EDGEONE_ALLOW_V6 -n         # IPv6规则
```

## 高级配置

### 1. 自定义端口配置

#### 交互式配置
```bash
sudo update_edgeone_ufw.sh -i
# 或
sudo update_edgeone_iptables.sh -i
```

交互式界面会显示：
```
=== 交互式端口配置 ===
当前默认端口: 80,443,8080,8443

常用端口参考:
  Web服务: 80,443
  SSH: 22
  自定义服务: 53717

请输入要配置的端口列表（用逗号分隔，回车使用默认）: 53717
已设置端口: 53717

是否继续执行更新？(y/N): y
```

#### 命令行指定端口
```bash
# 单个端口
sudo update_edgeone_ufw.sh -p "53717"

# 多个端口
sudo update_edgeone_ufw.sh -p "80,443,53717"

# Web服务端口
sudo update_edgeone_ufw.sh -p "80,443,8080,8443"
```

### 2. 自定义链名称（仅iptables版本）

```bash
# 使用自定义链名称
sudo update_edgeone_iptables.sh --chain-name MY_EDGEONE_CHAIN -p "53717"

# 查看自定义链
sudo iptables -L MY_EDGEONE_CHAIN -n
```

### 3. 详细输出和调试

```bash
# 详细模式
sudo update_edgeone_ufw.sh -v
sudo update_edgeone_iptables.sh -v

# 预览+详细模式
sudo update_edgeone_ufw.sh --dry-run -v
sudo update_edgeone_iptables.sh --dry-run -v

# 交互式+预览+详细模式
sudo update_edgeone_ufw.sh -i --dry-run -v
sudo update_edgeone_iptables.sh -i --dry-run -v
```

## 自动化和定时任务

### 1. systemd 定时器管理

#### UFW 版本
```bash
# 查看定时器状态
sudo systemctl status edgeone-ufw-update.timer

# 启用/禁用定时器
sudo systemctl enable edgeone-ufw-update.timer
sudo systemctl disable edgeone-ufw-update.timer

# 手动触发更新
sudo systemctl start edgeone-ufw-update.service

# 查看日志
sudo journalctl -u edgeone-ufw-update.service -f
```

#### iptables 版本
```bash
# 查看定时器状态
sudo systemctl status edgeone-iptables-update.timer

# 启用/禁用定时器
sudo systemctl enable edgeone-iptables-update.timer
sudo systemctl disable edgeone-iptables-update.timer

# 手动触发更新
sudo systemctl start edgeone-iptables-update.service

# 查看日志
sudo journalctl -u edgeone-iptables-update.service -f
```

### 2. 查看下次执行时间

```bash
# UFW版本
sudo systemctl list-timers edgeone-ufw-update.timer

# iptables版本
sudo systemctl list-timers edgeone-iptables-update.timer
```

### 3. 修改执行时间

```bash
# 编辑定时器配置
sudo systemctl edit edgeone-ufw-update.timer
# 或
sudo systemctl edit edgeone-iptables-update.timer

# 添加自定义配置
[Timer]
OnCalendar=*-*-* 06:00:00  # 改为每天6点
RandomizedDelaySec=600     # 随机延迟10分钟
```

## 日志管理

### 1. 查看日志

```bash
# 实时查看更新日志
sudo tail -f /var/log/edgeone_ufw_update.log      # UFW版本
sudo tail -f /var/log/edgeone_iptables_update.log # iptables版本

# 查看历史日志
sudo cat /var/log/edgeone_ufw_update.log | tail -50
sudo cat /var/log/edgeone_iptables_update.log | tail -50

# 查看systemd服务日志
sudo journalctl -u edgeone-ufw-update.service --since "1 hour ago"
sudo journalctl -u edgeone-iptables-update.service --since "1 hour ago"
```

### 2. 日志分析

```bash
# 查看最近的成功更新
grep "脚本执行完成" /var/log/edgeone_ufw_update.log | tail -5

# 查看错误信息
grep "ERROR" /var/log/edgeone_ufw_update.log

# 查看规则统计
grep "规则数量" /var/log/edgeone_ufw_update.log | tail -5
```

## 规则管理

### 1. 查看当前规则

#### UFW 版本
```bash
# 查看所有EdgeOne规则
sudo ufw status | grep EdgeOne

# 查看规则数量
sudo ufw status | grep -c EdgeOne

# 查看详细规则信息
sudo ufw status verbose | grep EdgeOne
```

#### iptables 版本
```bash
# 查看IPv4规则
sudo iptables -L EDGEONE_ALLOW -n --line-numbers

# 查看IPv6规则
sudo ip6tables -L EDGEONE_ALLOW_V6 -n --line-numbers

# 查看规则统计
echo "IPv4规则数量: $(sudo iptables -L EDGEONE_ALLOW | grep -c ACCEPT)"
echo "IPv6规则数量: $(sudo ip6tables -L EDGEONE_ALLOW_V6 | grep -c ACCEPT)"
```

### 2. 手动管理规则

#### UFW 版本
```bash
# 手动添加规则
sudo ufw allow from *******/32 to any port 53717 comment 'EdgeOne-Manual'

# 删除特定规则
sudo ufw status numbered | grep EdgeOne
sudo ufw delete [规则编号]

# 删除所有EdgeOne规则
sudo update_edgeone_ufw.sh --remove-all
```

#### iptables 版本
```bash
# 手动添加规则
sudo iptables -A EDGEONE_ALLOW -s *******/32 -p tcp --dport 53717 -j ACCEPT

# 删除特定规则
sudo iptables -L EDGEONE_ALLOW --line-numbers
sudo iptables -D EDGEONE_ALLOW [行号]

# 清空所有EdgeOne规则
sudo update_edgeone_iptables.sh --remove-all
```

### 3. 备份和恢复

#### UFW 版本
```bash
# 备份UFW规则
sudo cp /etc/ufw/user.rules /etc/ufw/user.rules.backup
sudo cp /etc/ufw/user6.rules /etc/ufw/user6.rules.backup

# 导出规则列表
sudo ufw status numbered > ufw_rules_backup.txt
```

#### iptables 版本
```bash
# 备份iptables规则
sudo iptables-save > iptables_backup.txt
sudo ip6tables-save > ip6tables_backup.txt

# 恢复规则
sudo iptables-restore < iptables_backup.txt
sudo ip6tables-restore < ip6tables_backup.txt
```

## 故障排除

### 1. 常见问题

#### API连接失败
```bash
# 测试API连接
curl -s https://api.edgeone.ai/ips | head -5

# 检查网络连接
ping api.edgeone.ai

# 检查DNS解析
nslookup api.edgeone.ai
```

#### 权限问题
```bash
# 确保使用sudo运行
sudo update_edgeone_ufw.sh

# 检查脚本权限
ls -la /usr/local/bin/update_edgeone_*

# 重新设置权限
sudo chmod +x /usr/local/bin/update_edgeone_*.sh
```

#### 防火墙问题
```bash
# UFW版本：检查UFW状态
sudo ufw status

# iptables版本：检查iptables状态
sudo iptables -L

# 重启防火墙服务
sudo systemctl restart ufw        # UFW
sudo systemctl restart iptables   # iptables
```

### 2. 调试命令

```bash
# 运行测试脚本
sudo ./test.sh

# 详细模式运行
sudo update_edgeone_ufw.sh -v --dry-run
sudo update_edgeone_iptables.sh -v --dry-run

# 检查systemd服务状态
sudo systemctl status edgeone-ufw-update.timer
sudo systemctl status edgeone-iptables-update.timer
```

## 卸载

```bash
# 卸载所有版本
sudo ./install.sh uninstall

# 手动清理残留规则（可选）
sudo update_edgeone_ufw.sh --remove-all      # 如果UFW版本仍可用
sudo update_edgeone_iptables.sh --remove-all # 如果iptables版本仍可用
```

## 最佳实践

1. **首次使用**：先运行 `--dry-run` 模式预览操作
2. **定期检查**：定期查看日志确保正常运行
3. **备份规则**：定期备份防火墙规则
4. **测试环境**：在生产环境部署前先在测试环境验证
5. **监控告警**：设置日志监控和告警机制
6. **文档记录**：记录自定义配置和变更历史

这个完整的使用指南涵盖了从安装到日常维护的所有方面，帮助您充分利用 EdgeOne 防火墙更新器的功能。

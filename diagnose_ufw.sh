#!/bin/bash

# UFW诊断和修复脚本
# 用于诊断EdgeOne UFW更新器的问题

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限。请使用sudo运行。"
        exit 1
    fi
}

# 检查UFW安装状态
check_ufw_installation() {
    log_step "检查UFW安装状态..."
    
    if command -v ufw &> /dev/null; then
        local ufw_version=$(ufw --version 2>/dev/null | head -1 || echo "未知版本")
        log_info "UFW已安装: $ufw_version"
        return 0
    else
        log_error "UFW未安装"
        log_info "安装命令:"
        echo "  Ubuntu/Debian: sudo apt update && sudo apt install ufw"
        echo "  CentOS/RHEL: sudo yum install ufw 或 sudo dnf install ufw"
        return 1
    fi
}

# 检查UFW状态
check_ufw_status() {
    log_step "检查UFW状态..."
    
    local status_output=$(ufw status verbose 2>&1)
    echo "$status_output"
    
    if echo "$status_output" | grep -q "Status: active"; then
        log_info "UFW已启用"
        return 0
    elif echo "$status_output" | grep -q "Status: inactive"; then
        log_warn "UFW未启用"
        return 1
    else
        log_error "无法确定UFW状态"
        return 2
    fi
}

# 启用UFW
enable_ufw() {
    log_step "启用UFW..."
    
    if ufw --force enable; then
        log_info "UFW已成功启用"
        return 0
    else
        log_error "无法启用UFW"
        return 1
    fi
}

# 测试UFW规则添加
test_ufw_rule_addition() {
    log_step "测试UFW规则添加..."
    
    local test_ip="*************"
    local test_port="12345"
    local test_comment="EdgeOne-Test-Rule"
    
    # 尝试添加测试规则
    log_info "添加测试规则: $test_ip -> 端口 $test_port"
    
    if ufw allow from "$test_ip" to any port "$test_port" comment "$test_comment" 2>/dev/null; then
        log_info "✓ 测试规则添加成功"
        
        # 验证规则是否存在
        if ufw status | grep -q "$test_ip"; then
            log_info "✓ 测试规则验证成功"
        else
            log_warn "测试规则添加成功但未在状态中显示"
        fi
        
        # 删除测试规则
        log_info "删除测试规则..."
        local rule_num=$(ufw status numbered | grep "$test_ip" | awk '{print $1}' | tr -d '[]')
        if [[ -n "$rule_num" ]]; then
            ufw --force delete "$rule_num" &>/dev/null
            log_info "✓ 测试规则已删除"
        fi
        
        return 0
    else
        log_error "✗ 测试规则添加失败"
        return 1
    fi
}

# 检查EdgeOne规则
check_edgeone_rules() {
    log_step "检查现有EdgeOne规则..."
    
    local rule_count=$(ufw status | grep -c "EdgeOne" 2>/dev/null || echo 0)
    log_info "现有EdgeOne规则数量: $rule_count"
    
    if [[ $rule_count -gt 0 ]]; then
        log_info "EdgeOne规则示例:"
        ufw status | grep "EdgeOne" | head -5 | sed 's/^/  /'
        
        if [[ $rule_count -gt 5 ]]; then
            log_info "  ... 还有 $((rule_count - 5)) 条规则"
        fi
    fi
}

# 测试EdgeOne API连接
test_edgeone_api() {
    log_step "测试EdgeOne API连接..."
    
    local api_url="https://api.edgeone.ai/ips"
    local temp_file="/tmp/edgeone_api_test.txt"
    
    if curl -s --connect-timeout 10 --max-time 30 "$api_url" > "$temp_file" 2>/dev/null; then
        if [[ -s "$temp_file" ]]; then
            local line_count=$(wc -l < "$temp_file")
            log_info "✓ API连接成功，获取到 $line_count 个IP段"
            
            # 显示前几行作为示例
            log_info "IP段示例:"
            head -3 "$temp_file" | sed 's/^/  /'
        else
            log_error "✗ API返回空数据"
        fi
    else
        log_error "✗ 无法连接到EdgeOne API"
        log_info "请检查网络连接和防火墙设置"
    fi
    
    rm -f "$temp_file"
}

# 检查系统资源
check_system_resources() {
    log_step "检查系统资源..."
    
    # 检查内存使用
    local mem_info=$(free -h | grep "Mem:")
    log_info "内存使用: $mem_info"
    
    # 检查磁盘空间
    local disk_info=$(df -h / | tail -1)
    log_info "磁盘使用: $disk_info"
    
    # 检查UFW规则数量限制
    local total_rules=$(ufw status numbered | wc -l)
    log_info "当前UFW规则总数: $total_rules"
    
    if [[ $total_rules -gt 1000 ]]; then
        log_warn "UFW规则数量较多，可能影响性能"
    fi
}

# 修复常见问题
fix_common_issues() {
    log_step "尝试修复常见问题..."
    
    # 重新加载UFW
    log_info "重新加载UFW配置..."
    if ufw --force reload &>/dev/null; then
        log_info "✓ UFW配置重新加载成功"
    else
        log_warn "UFW配置重新加载失败"
    fi
    
    # 检查并修复UFW配置文件权限
    local ufw_config_dir="/etc/ufw"
    if [[ -d "$ufw_config_dir" ]]; then
        log_info "检查UFW配置文件权限..."
        chown -R root:root "$ufw_config_dir" 2>/dev/null || true
        chmod -R 644 "$ufw_config_dir"/*.rules 2>/dev/null || true
        log_info "✓ UFW配置文件权限已修复"
    fi
    
    # 清理可能的锁文件
    local lock_files=("/var/lib/ufw/lock" "/run/ufw.lock")
    for lock_file in "${lock_files[@]}"; do
        if [[ -f "$lock_file" ]]; then
            log_info "删除UFW锁文件: $lock_file"
            rm -f "$lock_file"
        fi
    done
}

# 生成诊断报告
generate_report() {
    log_step "生成诊断报告..."
    
    local report_file="/tmp/edgeone_ufw_diagnosis_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
EdgeOne UFW诊断报告
生成时间: $(date)
系统信息: $(uname -a)
UFW版本: $(ufw --version 2>/dev/null | head -1 || echo "未安装")

=== UFW状态 ===
$(ufw status verbose 2>&1)

=== EdgeOne规则统计 ===
EdgeOne规则数量: $(ufw status | grep -c "EdgeOne" 2>/dev/null || echo 0)

=== 系统资源 ===
内存: $(free -h | grep "Mem:")
磁盘: $(df -h / | tail -1)

=== 网络连接测试 ===
EdgeOne API测试: $(curl -s --connect-timeout 5 https://api.edgeone.ai/ips | wc -l 2>/dev/null || echo "失败")

=== 进程信息 ===
UFW相关进程: $(ps aux | grep -v grep | grep ufw || echo "无")

=== 日志信息 ===
最近的UFW日志:
$(tail -10 /var/log/ufw.log 2>/dev/null || echo "无日志文件")

EOF
    
    log_info "诊断报告已生成: $report_file"
    echo "您可以查看完整报告: cat $report_file"
}

# 主函数
main() {
    echo "EdgeOne UFW诊断工具"
    echo "==================="
    echo
    
    check_root
    
    local issues_found=0
    
    # 执行各项检查
    if ! check_ufw_installation; then
        issues_found=$((issues_found + 1))
    fi
    
    if ! check_ufw_status; then
        log_info "尝试启用UFW..."
        if enable_ufw; then
            log_info "UFW已启用，继续检查..."
        else
            issues_found=$((issues_found + 1))
        fi
    fi
    
    if ! test_ufw_rule_addition; then
        issues_found=$((issues_found + 1))
    fi
    
    check_edgeone_rules
    test_edgeone_api
    check_system_resources
    
    # 如果发现问题，尝试修复
    if [[ $issues_found -gt 0 ]]; then
        echo
        log_warn "发现 $issues_found 个问题，尝试修复..."
        fix_common_issues
        echo
        log_info "修复完成，建议重新运行诊断"
    else
        log_info "所有检查通过，UFW工作正常"
    fi
    
    # 生成报告
    generate_report
    
    echo
    echo "诊断完成！"
    echo
    echo "如果问题仍然存在，请："
    echo "1. 查看详细的诊断报告"
    echo "2. 检查系统日志: journalctl -u ufw"
    echo "3. 手动测试UFW命令: ufw allow from 192.168.1.1 to any port 80"
    echo "4. 联系系统管理员获取帮助"
}

# 执行主函数
main "$@"

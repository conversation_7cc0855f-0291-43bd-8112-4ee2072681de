#!/bin/bash

# EdgeOne iptables Rules Update Script
# 自动获取腾讯云EdgeOne IP列表并更新iptables防火墙规则
# 支持IPv4和IPv6，智能对比增删规则

set -eo pipefail

# 配置变量
API_URL="https://api.edgeone.ai/ips"
TEMP_DIR="/tmp/edgeone_iptables"
CURRENT_IPS_FILE="$TEMP_DIR/current_ips.txt"
PREVIOUS_IPS_FILE="$TEMP_DIR/previous_ips.txt"
LOG_FILE="/var/log/edgeone_iptables_update.log"
CHAIN_NAME="EDGEONE_ALLOW"
CHAIN_NAME_V6="EDGEONE_ALLOW_V6"

# 默认端口配置
DEFAULT_PORTS="80,443,8080,8443"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ${1:-}" | tee -a "$LOG_FILE"
}

# 错误处理
error_exit() {
    log "ERROR: ${1:-Unknown error}"
    exit 1
}

# 显示帮助信息
show_help() {
    cat << EOF
EdgeOne iptables Rules Update Script

用法: $0 [选项]

选项:
    -p, --ports PORTS       指定端口列表，用逗号分隔 (默认: $DEFAULT_PORTS)
    -i, --interactive      交互式模式，提示用户输入端口
    -d, --dry-run          仅显示将要执行的操作，不实际修改防火墙规则
    -v, --verbose          详细输出模式
    -h, --help             显示此帮助信息
    --remove-all           删除所有EdgeOne相关的iptables规则
    --chain-name NAME      自定义链名称 (默认: $CHAIN_NAME)

示例:
    $0                                    # 使用默认端口更新规则
    $0 -p "53717"                        # 指定端口更新规则
    $0 -i                                # 交互式输入端口
    $0 --dry-run                         # 预览模式
    $0 --remove-all                      # 删除所有EdgeOne规则

注意:
    - 脚本需要root权限来修改iptables规则
    - 建议先运行 --dry-run 预览模式
    - 当前默认端口: $DEFAULT_PORTS

EOF
}

# 解析命令行参数
PORTS="$DEFAULT_PORTS"
DRY_RUN=false
VERBOSE=false
REMOVE_ALL=false
INTERACTIVE=false

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        local arg="${1:-}"
        case "$arg" in
            -p|--ports)
                PORTS="${2:-$DEFAULT_PORTS}"
                shift 2 || shift
                ;;
            -i|--interactive)
                INTERACTIVE=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --remove-all)
                REMOVE_ALL=true
                shift
                ;;
            --chain-name)
                CHAIN_NAME="${2:-$CHAIN_NAME}"
                CHAIN_NAME_V6="${CHAIN_NAME}_V6"
                shift 2 || shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            start|run|execute|"")
                shift || break
                ;;
            *)
                echo "未知参数: $arg"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查权限和依赖
check_permissions() {
    if [[ $EUID -ne 0 ]] && [[ "$DRY_RUN" == false ]]; then
        error_exit "此脚本需要root权限来修改iptables规则。请使用sudo运行。"
    fi
    
    # 检查iptables是否安装
    if ! command -v iptables &> /dev/null; then
        error_exit "iptables未安装。请先安装iptables"
    fi
    
    # 检查ip6tables是否安装（用于IPv6）
    if ! command -v ip6tables &> /dev/null; then
        log "WARNING: ip6tables未安装，将跳过IPv6规则"
    fi
}

# 创建临时目录
setup_temp_dir() {
    mkdir -p "$TEMP_DIR"
    
    if [[ -f "$CURRENT_IPS_FILE" ]]; then
        cp "$CURRENT_IPS_FILE" "$PREVIOUS_IPS_FILE"
    fi
}

# 获取EdgeOne IP列表
fetch_edgeone_ips() {
    log "正在获取EdgeOne IP列表..."
    
    if ! curl -s --connect-timeout 30 --max-time 60 "$API_URL" > "$CURRENT_IPS_FILE"; then
        error_exit "无法获取EdgeOne IP列表"
    fi
    
    if [[ ! -s "$CURRENT_IPS_FILE" ]]; then
        error_exit "获取的IP列表为空"
    fi
    
    local ip_count=$(wc -l < "$CURRENT_IPS_FILE")
    log "成功获取 $ip_count 个IP段"
    
    if [[ "$VERBOSE" == true ]]; then
        log "IP列表预览（前5行）:"
        head -5 "$CURRENT_IPS_FILE" | while read line; do
            log "  $line"
        done
    fi
}

# 交互式输入端口
interactive_port_input() {
    echo
    echo "=== 交互式端口配置 ==="
    echo "当前默认端口: $DEFAULT_PORTS"
    echo
    echo "常用端口参考:"
    echo "  Web服务: 80,443"
    echo "  SSH: 22"
    echo "  自定义服务: 53717"
    echo
    
    while true; do
        read -p "请输入要配置的端口列表（用逗号分隔，回车使用默认）: " user_ports
        
        if [[ -z "$user_ports" ]]; then
            PORTS="$DEFAULT_PORTS"
            echo "使用默认端口: $PORTS"
            break
        fi
        
        if validate_ports "$user_ports"; then
            PORTS="$user_ports"
            echo "已设置端口: $PORTS"
            break
        else
            echo "端口格式错误，请重新输入。格式示例: 80,443,53717"
        fi
    done
    
    echo
    read -p "是否继续执行更新？(y/N): " confirm
    if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
        echo "操作已取消"
        exit 0
    fi
}

# 验证端口格式
validate_ports() {
    local ports="${1:-}"
    
    if [[ ! "$ports" =~ ^[0-9,[:space:]]+$ ]]; then
        return 1
    fi
    
    IFS=',' read -ra PORT_ARRAY <<< "$ports"
    for port in "${PORT_ARRAY[@]}"; do
        port=$(echo "$port" | xargs)
        
        if [[ ! "$port" =~ ^[0-9]+$ ]] || [[ "$port" -lt 1 ]] || [[ "$port" -gt 65535 ]]; then
            echo "错误: 端口 '$port' 无效。端口必须在 1-65535 范围内。"
            return 1
        fi
    done
    
    return 0
}

# 分离IPv4和IPv6地址
separate_ip_versions() {
    local input_file="${1:-}"
    local ipv4_file="$TEMP_DIR/ipv4_list.txt"
    local ipv6_file="$TEMP_DIR/ipv6_list.txt"
    
    > "$ipv4_file"
    > "$ipv6_file"
    
    if [[ -f "$input_file" ]]; then
        while IFS= read -r line; do
            if [[ "$line" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/ ]]; then
                echo "$line" >> "$ipv4_file"
            elif [[ "$line" =~ : ]]; then
                echo "$line" >> "$ipv6_file"
            fi
        done < "$input_file"
    fi
    
    echo "$ipv4_file $ipv6_file"
}

# 创建自定义链
create_custom_chains() {
    if [[ "$DRY_RUN" == true ]]; then
        echo "DRY-RUN: 创建iptables自定义链 $CHAIN_NAME"
        echo "DRY-RUN: 创建ip6tables自定义链 $CHAIN_NAME_V6"
        return
    fi
    
    # 创建IPv4链
    if ! iptables -L "$CHAIN_NAME" &>/dev/null; then
        iptables -N "$CHAIN_NAME"
        log "创建IPv4自定义链: $CHAIN_NAME"
    fi
    
    # 创建IPv6链
    if command -v ip6tables &> /dev/null; then
        if ! ip6tables -L "$CHAIN_NAME_V6" &>/dev/null; then
            ip6tables -N "$CHAIN_NAME_V6"
            log "创建IPv6自定义链: $CHAIN_NAME_V6"
        fi
    fi
}

# 清空自定义链
flush_custom_chains() {
    if [[ "$DRY_RUN" == true ]]; then
        echo "DRY-RUN: 清空自定义链规则"
        return
    fi
    
    # 清空IPv4链
    if iptables -L "$CHAIN_NAME" &>/dev/null; then
        iptables -F "$CHAIN_NAME"
        log "清空IPv4链: $CHAIN_NAME"
    fi
    
    # 清空IPv6链
    if command -v ip6tables &> /dev/null && ip6tables -L "$CHAIN_NAME_V6" &>/dev/null; then
        ip6tables -F "$CHAIN_NAME_V6"
        log "清空IPv6链: $CHAIN_NAME_V6"
    fi
}

# 添加iptables规则
add_iptables_rule() {
    local ip_range="${1:-}"
    local port="${2:-}"
    local ip_version="${3:-4}"
    
    local cmd="iptables"
    local chain="$CHAIN_NAME"
    
    if [[ "$ip_version" == "6" ]]; then
        cmd="ip6tables"
        chain="$CHAIN_NAME_V6"
        
        if ! command -v ip6tables &> /dev/null; then
            log "WARNING: 跳过IPv6规则，ip6tables未安装"
            return
        fi
    fi
    
    if [[ "$DRY_RUN" == true ]]; then
        echo "DRY-RUN: $cmd -A $chain -s $ip_range -p tcp --dport $port -j ACCEPT"
        return
    fi
    
    if $cmd -A "$chain" -s "$ip_range" -p tcp --dport "$port" -j ACCEPT 2>/dev/null; then
        if [[ "$VERBOSE" == true ]]; then
            log "添加IPv${ip_version}规则: $ip_range -> 端口 $port"
        fi
    else
        log "WARNING: 无法添加IPv${ip_version}规则 $ip_range -> 端口 $port"
    fi
}

# 链接自定义链到INPUT链
link_chains_to_input() {
    if [[ "$DRY_RUN" == true ]]; then
        echo "DRY-RUN: 链接自定义链到INPUT链"
        return
    fi
    
    # 检查IPv4链是否已链接
    if ! iptables -C INPUT -j "$CHAIN_NAME" 2>/dev/null; then
        iptables -I INPUT -j "$CHAIN_NAME"
        log "链接IPv4链到INPUT: $CHAIN_NAME"
    fi
    
    # 检查IPv6链是否已链接
    if command -v ip6tables &> /dev/null; then
        if ! ip6tables -C INPUT -j "$CHAIN_NAME_V6" 2>/dev/null; then
            ip6tables -I INPUT -j "$CHAIN_NAME_V6"
            log "链接IPv6链到INPUT: $CHAIN_NAME_V6"
        fi
    fi
}

# 删除所有EdgeOne规则
remove_all_edgeone_rules() {
    log "正在删除所有EdgeOne相关的iptables规则..."
    
    if [[ "$DRY_RUN" == true ]]; then
        echo "DRY-RUN: 删除所有EdgeOne iptables规则"
        return
    fi
    
    # 从INPUT链中移除自定义链的引用
    iptables -D INPUT -j "$CHAIN_NAME" 2>/dev/null || true
    if command -v ip6tables &> /dev/null; then
        ip6tables -D INPUT -j "$CHAIN_NAME_V6" 2>/dev/null || true
    fi
    
    # 清空并删除自定义链
    iptables -F "$CHAIN_NAME" 2>/dev/null || true
    iptables -X "$CHAIN_NAME" 2>/dev/null || true
    
    if command -v ip6tables &> /dev/null; then
        ip6tables -F "$CHAIN_NAME_V6" 2>/dev/null || true
        ip6tables -X "$CHAIN_NAME_V6" 2>/dev/null || true
    fi
    
    log "EdgeOne iptables规则删除完成"
}

# 主函数
main() {
    log "EdgeOne iptables更新脚本开始执行"
    
    # 解析参数
    parse_arguments "$@"
    
    # 检查权限和依赖
    check_permissions
    
    # 设置临时目录
    setup_temp_dir
    
    # 处理删除所有规则的情况
    if [[ "$REMOVE_ALL" == true ]]; then
        remove_all_edgeone_rules
        log "脚本执行完成"
        exit 0
    fi
    
    # 交互式端口输入
    if [[ "$INTERACTIVE" == true ]]; then
        interactive_port_input
    fi
    
    # 验证端口格式
    if ! validate_ports "$PORTS"; then
        error_exit "端口格式错误: $PORTS"
    fi
    
    log "使用端口: $PORTS"
    
    # 获取最新IP列表
    fetch_edgeone_ips
    
    # 开始更新iptables规则
    log "开始更新iptables规则..."
    
    # 分离IPv4和IPv6
    local files=($(separate_ip_versions "$CURRENT_IPS_FILE"))
    local ipv4_file="${files[0]}"
    local ipv6_file="${files[1]}"
    
    log "IPv4地址数量: $(wc -l < "$ipv4_file")"
    log "IPv6地址数量: $(wc -l < "$ipv6_file")"
    
    # 创建自定义链
    create_custom_chains
    
    # 清空现有规则
    flush_custom_chains
    
    # 添加规则
    IFS=',' read -ra PORT_ARRAY <<< "$PORTS"
    
    # 处理IPv4规则
    if [[ -s "$ipv4_file" ]]; then
        log "添加IPv4规则..."
        while IFS= read -r ip_range; do
            [[ -z "$ip_range" ]] && continue
            
            for port in "${PORT_ARRAY[@]}"; do
                port=$(echo "$port" | xargs)
                add_iptables_rule "$ip_range" "$port" "4"
            done
        done < "$ipv4_file"
    fi
    
    # 处理IPv6规则
    if [[ -s "$ipv6_file" ]] && command -v ip6tables &> /dev/null; then
        log "添加IPv6规则..."
        while IFS= read -r ip_range; do
            [[ -z "$ip_range" ]] && continue
            
            for port in "${PORT_ARRAY[@]}"; do
                port=$(echo "$port" | xargs)
                add_iptables_rule "$ip_range" "$port" "6"
            done
        done < "$ipv6_file"
    fi
    
    # 链接自定义链到INPUT链
    link_chains_to_input
    
    log "iptables规则更新完成"
    
    # 显示统计信息
    if [[ "$VERBOSE" == true ]] || [[ "$DRY_RUN" == true ]]; then
        local ipv4_rules=$(iptables -L "$CHAIN_NAME" --line-numbers 2>/dev/null | grep -c "ACCEPT" || echo 0)
        local ipv6_rules=0
        if command -v ip6tables &> /dev/null; then
            ipv6_rules=$(ip6tables -L "$CHAIN_NAME_V6" --line-numbers 2>/dev/null | grep -c "ACCEPT" || echo 0)
        fi
        log "当前EdgeOne规则数量: IPv4=$ipv4_rules, IPv6=$ipv6_rules"
    fi
    
    log "脚本执行完成"
}

# 信号处理
trap 'log "脚本被中断"; exit 1' INT TERM

# 执行主函数
main "$@"

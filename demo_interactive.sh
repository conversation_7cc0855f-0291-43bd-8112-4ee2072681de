#!/bin/bash

# EdgeOne UFW 交互式演示脚本
# 演示如何使用交互式端口输入功能

echo "EdgeOne UFW 更新器 - 交互式演示"
echo "================================"
echo
echo "要使用交互式端口输入功能，请运行以下命令之一："
echo
echo "1. 交互式模式 + 预览模式（推荐先测试）："
echo "   sudo ./update_edgeone_ufw.sh -i --dry-run"
echo
echo "2. 交互式模式 + 详细输出："
echo "   sudo ./update_edgeone_ufw.sh -i -v"
echo
echo "3. 仅交互式模式："
echo "   sudo ./update_edgeone_ufw.sh -i"
echo
echo "4. 交互式 + 预览 + 详细输出（完整测试）："
echo "   sudo ./update_edgeone_ufw.sh -i -d -v"
echo
echo "参数说明："
echo "  -i, --interactive  : 启用交互式端口输入"
echo "  -d, --dry-run     : 预览模式，不实际修改规则"
echo "  -v, --verbose     : 详细输出"
echo
echo "如果您想要立即测试交互式功能，请选择一个选项："

options=(
    "交互式 + 预览模式（安全测试）"
    "查看帮助信息"
    "退出"
)

select opt in "${options[@]}"; do
    case $opt in
        "交互式 + 预览模式（安全测试）")
            echo
            echo "正在启动交互式预览模式..."
            echo "命令: sudo ./update_edgeone_ufw.sh -i --dry-run"
            echo
            if [[ -f "./update_edgeone_ufw.sh" ]]; then
                sudo ./update_edgeone_ufw.sh -i --dry-run
            else
                echo "错误: 找不到 update_edgeone_ufw.sh 脚本文件"
                echo "请确保您在正确的目录中运行此演示脚本"
            fi
            break
            ;;
        "查看帮助信息")
            echo
            if [[ -f "./update_edgeone_ufw.sh" ]]; then
                ./update_edgeone_ufw.sh --help
            else
                echo "错误: 找不到 update_edgeone_ufw.sh 脚本文件"
            fi
            break
            ;;
        "退出")
            echo "退出演示"
            break
            ;;
        *)
            echo "无效选项，请重新选择"
            ;;
    esac
done

echo
echo "其他有用的命令："
echo "  查看当前EdgeOne规则: sudo ufw status | grep EdgeOne"
echo "  删除所有EdgeOne规则: sudo ./update_edgeone_ufw.sh --remove-all"
echo "  查看脚本日志: sudo tail -f /var/log/edgeone_ufw_update.log"

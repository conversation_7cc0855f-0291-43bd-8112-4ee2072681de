#!/bin/bash

# 简单的UFW测试脚本
# 用于快速测试UFW规则添加功能

echo "EdgeOne UFW 简单测试"
echo "==================="

# 检查权限
if [[ $EUID -ne 0 ]]; then
    echo "错误: 需要root权限，请使用sudo运行"
    exit 1
fi

# 检查UFW
if ! command -v ufw &> /dev/null; then
    echo "错误: UFW未安装"
    exit 1
fi

echo "1. 当前UFW状态:"
ufw status

echo
echo "2. 测试添加单个规则:"
echo "命令: ufw allow from 192.168.1.100 to any port 53717 comment 'EdgeOne-Test'"

if ufw allow from 192.168.1.100 to any port 53717 comment 'EdgeOne-Test'; then
    echo "✓ 规则添加成功"
    
    echo
    echo "3. 验证规则是否存在:"
    if ufw status | grep -q "192.168.1.100"; then
        echo "✓ 规则验证成功"
        ufw status | grep "192.168.1.100"
    else
        echo "✗ 规则未在状态中显示"
    fi
    
    echo
    echo "4. 删除测试规则:"
    rule_num=$(ufw status numbered | grep "192.168.1.100" | awk '{print $1}' | tr -d '[]')
    if [[ -n "$rule_num" ]]; then
        if ufw --force delete "$rule_num"; then
            echo "✓ 测试规则已删除"
        else
            echo "✗ 删除测试规则失败"
        fi
    else
        echo "✗ 找不到测试规则编号"
    fi
else
    echo "✗ 规则添加失败"
    echo
    echo "可能的原因:"
    echo "1. UFW未启用 - 运行: sudo ufw enable"
    echo "2. 权限问题 - 确保使用sudo运行"
    echo "3. UFW配置问题 - 运行: sudo ufw --force reload"
    echo "4. 系统资源不足"
    
    echo
    echo "尝试启用UFW:"
    if ufw --force enable; then
        echo "✓ UFW已启用，请重新运行测试"
    else
        echo "✗ 无法启用UFW"
    fi
fi

echo
echo "5. 测试EdgeOne API连接:"
if curl -s --connect-timeout 5 https://api.edgeone.ai/ips | head -3; then
    echo "✓ API连接正常"
else
    echo "✗ API连接失败"
fi

echo
echo "测试完成！"

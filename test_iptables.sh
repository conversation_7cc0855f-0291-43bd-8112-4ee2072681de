#!/bin/bash

# iptables版本测试脚本

echo "EdgeOne iptables 版本测试"
echo "========================="

# 检查权限
if [[ $EUID -ne 0 ]]; then
    echo "错误: 需要root权限，请使用sudo运行"
    exit 1
fi

# 检查iptables
if ! command -v iptables &> /dev/null; then
    echo "错误: iptables未安装"
    exit 1
fi

echo "1. 当前iptables状态:"
iptables -L -n | head -20

echo
echo "2. 测试创建自定义链:"
CHAIN_NAME="EDGEONE_TEST"

# 创建测试链
if iptables -N "$CHAIN_NAME" 2>/dev/null; then
    echo "✓ 成功创建测试链: $CHAIN_NAME"
    
    # 添加测试规则
    if iptables -A "$CHAIN_NAME" -s ************* -p tcp --dport 53717 -j ACCEPT; then
        echo "✓ 成功添加测试规则"
        
        # 验证规则
        if iptables -L "$CHAIN_NAME" | grep -q "*************"; then
            echo "✓ 测试规则验证成功"
        else
            echo "✗ 测试规则未在链中显示"
        fi
        
        # 清理测试规则
        iptables -F "$CHAIN_NAME"
        echo "✓ 清理测试规则"
    else
        echo "✗ 无法添加测试规则"
    fi
    
    # 删除测试链
    iptables -X "$CHAIN_NAME"
    echo "✓ 删除测试链"
else
    echo "✗ 无法创建测试链"
fi

echo
echo "3. 测试EdgeOne API连接:"
if curl -s --connect-timeout 5 https://api.edgeone.ai/ips | head -3; then
    echo "✓ API连接正常"
else
    echo "✗ API连接失败"
fi

echo
echo "4. 检查ip6tables支持:"
if command -v ip6tables &> /dev/null; then
    echo "✓ ip6tables已安装，支持IPv6"
else
    echo "⚠ ip6tables未安装，将跳过IPv6规则"
fi

echo
echo "5. 检查iptables-save支持:"
if command -v iptables-save &> /dev/null; then
    echo "✓ iptables-save可用，支持规则持久化"
else
    echo "⚠ iptables-save不可用，规则可能在重启后丢失"
fi

echo
echo "测试完成！"
echo
echo "如果所有测试通过，可以运行:"
echo "  sudo ./update_edgeone_iptables.sh --dry-run"

# EdgeOne 防火墙更新器 - 文件清单

## 核心脚本文件

### 1. 主要更新脚本
- **`update_edgeone_ufw.sh`** - UFW版本的主更新脚本
  - 支持UFW防火墙规则管理
  - 智能对比和增量更新
  - 交互式端口配置
  - 适用于Ubuntu/Debian系统

- **`update_edgeone_iptables.sh`** - iptables版本的主更新脚本
  - 支持iptables防火墙规则管理
  - 自定义链管理
  - IPv4/IPv6双栈支持
  - 适用于所有Linux发行版

- **`update_edgeone_ufw_safe.sh`** - UFW安全版本（备用）
  - 移除了可能导致unbound variable错误的代码
  - 更保守的错误处理

### 2. 安装和管理脚本
- **`install.sh`** - 统一安装脚本
  - 支持选择防火墙类型
  - 自动配置systemd服务
  - 创建配置文件和日志轮转

## systemd 服务文件

### UFW 版本
- **`edgeone-ufw-update.service`** - UFW版本的systemd服务文件
- **`edgeone-ufw-update.timer`** - UFW版本的systemd定时器文件

### iptables 版本
- **`edgeone-iptables-update.service`** - iptables版本的systemd服务文件
- **`edgeone-iptables-update.timer`** - iptables版本的systemd定时器文件

## 测试和诊断脚本

- **`test.sh`** - 统一测试脚本
  - 自动检测已安装的版本
  - 全面的功能测试
  - 系统兼容性检查

- **`test_ufw_simple.sh`** - UFW简单测试脚本
  - 快速UFW功能验证
  - 基本规则添加测试

- **`test_iptables.sh`** - iptables测试脚本
  - iptables环境检查
  - 自定义链测试
  - IPv6支持检测

- **`test_interactive.sh`** - 交互式功能测试脚本
- **`diagnose_ufw.sh`** - UFW诊断和修复脚本

## 演示和示例脚本

- **`demo_interactive.sh`** - 交互式功能演示脚本
- **`setup_permissions.bat`** - Windows环境权限设置脚本（开发用）

## 文档文件

### 主要文档
- **`README.md`** - 项目主要说明文档
  - 功能特性介绍
  - 快速开始指南
  - 基本使用方法

- **`USAGE_GUIDE.md`** - 完整使用指南
  - 详细的安装步骤
  - 高级配置选项
  - 故障排除指南

### 专门指南
- **`IPTABLES_GUIDE.md`** - iptables版本专用指南
  - iptables版本特性
  - 高级配置和优化
  - 与UFW版本的对比

- **`USAGE_INTERACTIVE.md`** - 交互式功能使用指南
- **`CRON_SETUP.md`** - 定时任务配置指南
- **`TROUBLESHOOTING.md`** - 故障排除指南

### 其他文档
- **`FILES_LIST.md`** - 本文件，项目文件清单

## 配置示例文件

安装后会自动创建的配置文件：
- `/etc/edgeone-ufw-updater/config.conf` - UFW版本配置文件
- `/etc/edgeone-iptables-updater/config.conf` - iptables版本配置文件

## 文件使用说明

### 必需文件（核心功能）
```
install.sh                          # 安装脚本
update_edgeone_ufw.sh               # UFW版本主脚本
update_edgeone_iptables.sh          # iptables版本主脚本
edgeone-ufw-update.service          # UFW systemd服务
edgeone-ufw-update.timer            # UFW systemd定时器
edgeone-iptables-update.service     # iptables systemd服务
edgeone-iptables-update.timer       # iptables systemd定时器
README.md                           # 主要文档
```

### 推荐文件（完整功能）
```
test.sh                             # 测试脚本
USAGE_GUIDE.md                      # 完整使用指南
TROUBLESHOOTING.md                  # 故障排除指南
```

### 可选文件（增强功能）
```
test_ufw_simple.sh                  # UFW简单测试
test_iptables.sh                    # iptables测试
diagnose_ufw.sh                     # UFW诊断工具
IPTABLES_GUIDE.md                   # iptables专用指南
CRON_SETUP.md                       # 定时任务指南
```

## 部署检查清单

### 1. 文件完整性检查
```bash
# 检查核心文件是否存在
ls -la install.sh
ls -la update_edgeone_ufw.sh
ls -la update_edgeone_iptables.sh
ls -la edgeone-*-update.service
ls -la edgeone-*-update.timer
```

### 2. 权限设置
```bash
# 设置执行权限
chmod +x install.sh
chmod +x update_edgeone_*.sh
chmod +x test*.sh
chmod +x diagnose_*.sh
chmod +x demo_*.sh
```

### 3. 安装验证
```bash
# 运行安装
sudo ./install.sh

# 运行测试
sudo ./test.sh
```

## 版本历史

### v1.0.0 - 初始版本
- 基本的UFW规则更新功能
- 简单的IP列表获取和规则添加

### v2.0.0 - 智能对比版本
- 增加智能对比功能
- 支持增量更新
- 添加交互式端口配置

### v3.0.0 - 双防火墙支持版本
- 添加iptables版本支持
- 统一的安装和管理脚本
- 完善的测试和诊断工具
- 详细的文档和使用指南

## 维护说明

### 定期更新
- 检查EdgeOne API的变化
- 更新文档和示例
- 测试新的Linux发行版兼容性

### 问题反馈
- 收集用户反馈
- 修复发现的bug
- 优化性能和稳定性

### 功能增强
- 添加新的防火墙类型支持
- 增强监控和告警功能
- 提供更多自定义选项

这个文件清单确保了项目的完整性和可维护性，帮助用户了解每个文件的作用和重要性。

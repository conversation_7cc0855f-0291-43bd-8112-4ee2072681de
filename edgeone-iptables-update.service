[Unit]
Description=EdgeOne iptables Rules Update Service
After=network-online.target iptables.service
Wants=network-online.target
Requires=iptables.service

[Service]
Type=oneshot
User=root
Group=root
ExecStart=/usr/local/bin/update_edgeone_iptables.sh -v
StandardOutput=journal
StandardError=journal
TimeoutStartSec=300

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/tmp /var/log
PrivateTmp=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true

[Install]
WantedBy=multi-user.target

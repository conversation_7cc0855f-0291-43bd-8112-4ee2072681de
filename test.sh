#!/bin/bash

# EdgeOne UFW更新器测试脚本
# 用于验证安装和功能是否正常

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试结果统计
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    log_test "运行测试: $test_name"
    
    if eval "$test_command"; then
        log_info "✓ 测试通过: $test_name"
        TESTS_PASSED=$((TESTS_PASSED + 1))
        return 0
    else
        log_error "✗ 测试失败: $test_name"
        TESTS_FAILED=$((TESTS_FAILED + 1))
        return 1
    fi
}

# 测试API连接
test_api_connection() {
    local api_url="https://api.edgeone.ai/ips"
    local temp_file="/tmp/edgeone_test_api.txt"
    
    if curl -s --connect-timeout 10 --max-time 30 "$api_url" > "$temp_file"; then
        if [[ -s "$temp_file" ]]; then
            local line_count=$(wc -l < "$temp_file")
            if [[ $line_count -gt 0 ]]; then
                log_info "API返回 $line_count 行数据"
                rm -f "$temp_file"
                return 0
            fi
        fi
    fi
    
    rm -f "$temp_file"
    return 1
}

# 测试脚本存在性
test_script_exists() {
    [[ -f "/usr/local/bin/update_edgeone_ufw.sh" ]] && [[ -x "/usr/local/bin/update_edgeone_ufw.sh" ]]
}

# 测试systemd文件
test_systemd_files() {
    [[ -f "/etc/systemd/system/edgeone-ufw-update.service" ]] && \
    [[ -f "/etc/systemd/system/edgeone-ufw-update.timer" ]]
}

# 测试配置文件
test_config_files() {
    [[ -f "/etc/edgeone-ufw-updater/config.conf" ]]
}

# 测试UFW安装
test_ufw_installed() {
    command -v ufw &> /dev/null
}

# 测试脚本语法
test_script_syntax() {
    bash -n "/usr/local/bin/update_edgeone_ufw.sh"
}

# 测试dry-run模式
test_dry_run() {
    if [[ $EUID -eq 0 ]]; then
        /usr/local/bin/update_edgeone_ufw.sh --dry-run &> /dev/null
    else
        sudo /usr/local/bin/update_edgeone_ufw.sh --dry-run &> /dev/null
    fi
}

# 测试定时器状态
test_timer_status() {
    systemctl is-enabled edgeone-ufw-update.timer &> /dev/null
}

# 测试日志轮转配置
test_logrotate_config() {
    [[ -f "/etc/logrotate.d/edgeone-ufw-update" ]]
}

# 测试帮助信息
test_help_output() {
    if [[ $EUID -eq 0 ]]; then
        /usr/local/bin/update_edgeone_ufw.sh --help &> /dev/null
    else
        sudo /usr/local/bin/update_edgeone_ufw.sh --help &> /dev/null
    fi
}

# 测试IP分离功能
test_ip_separation() {
    local temp_dir="/tmp/edgeone_test"
    local test_file="$temp_dir/test_ips.txt"
    
    mkdir -p "$temp_dir"
    
    cat > "$test_file" << 'EOF'
***********/24
10.0.0.0/8
2001:db8::/32
**********/12
2404:3100:d:5::/64
EOF
    
    # 模拟IP分离逻辑
    local ipv4_count=$(grep -E '^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/' "$test_file" | wc -l)
    local ipv6_count=$(grep -E ':' "$test_file" | wc -l)
    
    rm -rf "$temp_dir"
    
    [[ $ipv4_count -eq 3 ]] && [[ $ipv6_count -eq 2 ]]
}

# 显示系统信息
show_system_info() {
    echo
    log_info "系统信息:"
    echo "  操作系统: $(lsb_release -d 2>/dev/null | cut -f2 || cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)"
    echo "  内核版本: $(uname -r)"
    echo "  UFW版本: $(ufw --version 2>/dev/null | head -1 || echo '未安装')"
    echo "  当前用户: $(whoami)"
    echo "  权限: $(if [[ $EUID -eq 0 ]]; then echo 'root'; else echo '普通用户'; fi)"
    echo
}

# 显示EdgeOne规则统计
show_edgeone_rules_stats() {
    if command -v ufw &> /dev/null; then
        local rule_count=$(ufw status | grep -c "EdgeOne" 2>/dev/null || echo 0)
        log_info "当前EdgeOne UFW规则数量: $rule_count"
        
        if [[ $rule_count -gt 0 ]]; then
            echo "  规则示例:"
            ufw status | grep "EdgeOne" | head -3 | sed 's/^/    /'
            if [[ $rule_count -gt 3 ]]; then
                echo "    ... 还有 $((rule_count - 3)) 条规则"
            fi
        fi
    fi
}

# 主测试函数
main() {
    echo "EdgeOne UFW更新器测试脚本"
    echo "=========================="
    
    show_system_info
    
    # 运行所有测试
    run_test "API连接测试" "test_api_connection"
    run_test "主脚本文件存在" "test_script_exists"
    run_test "systemd文件存在" "test_systemd_files"
    run_test "配置文件存在" "test_config_files"
    run_test "UFW已安装" "test_ufw_installed"
    run_test "脚本语法检查" "test_script_syntax"
    run_test "dry-run模式测试" "test_dry_run"
    run_test "定时器状态" "test_timer_status"
    run_test "日志轮转配置" "test_logrotate_config"
    run_test "帮助信息输出" "test_help_output"
    run_test "IP地址分离功能" "test_ip_separation"
    
    echo
    echo "测试结果统计:"
    echo "============="
    echo "  总测试数: $TESTS_TOTAL"
    echo "  通过: $TESTS_PASSED"
    echo "  失败: $TESTS_FAILED"
    echo "  成功率: $(( TESTS_PASSED * 100 / TESTS_TOTAL ))%"
    
    echo
    show_edgeone_rules_stats
    
    echo
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log_info "所有测试通过！EdgeOne UFW更新器工作正常。"
        
        echo
        echo "建议的下一步操作:"
        echo "  1. 查看定时器状态: sudo systemctl status edgeone-ufw-update.timer"
        echo "  2. 手动执行一次更新: sudo /usr/local/bin/update_edgeone_ufw.sh -v"
        echo "  3. 查看更新日志: sudo tail -f /var/log/edgeone_ufw_update.log"
        
        exit 0
    else
        log_error "有 $TESTS_FAILED 个测试失败，请检查安装或配置。"
        
        echo
        echo "故障排除建议:"
        echo "  1. 重新运行安装脚本: sudo ./install.sh"
        echo "  2. 检查系统日志: sudo journalctl -u edgeone-ufw-update.service"
        echo "  3. 验证网络连接: curl -s https://api.edgeone.ai/ips | head -5"
        
        exit 1
    fi
}

# 执行主函数
main "$@"

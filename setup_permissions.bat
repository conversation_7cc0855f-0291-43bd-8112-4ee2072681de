@echo off
echo Setting up permissions for EdgeOne UFW updater scripts...

REM This script is for Windows development environment
REM In Linux, you would run: chmod +x *.sh

echo.
echo Files created:
echo - update_edgeone_ufw.sh (Main update script)
echo - install.sh (Installation script)  
echo - test.sh (Test script)
echo - edgeone-ufw-update.service (Systemd service)
echo - edgeone-ufw-update.timer (Systemd timer)
echo - README.md (Documentation)

echo.
echo To use on Linux:
echo 1. Transfer these files to your Linux server
echo 2. Run: chmod +x *.sh
echo 3. Run: sudo ./install.sh
echo 4. Test: sudo ./test.sh

echo.
echo For immediate testing on Linux:
echo   curl -s "https://api.edgeone.ai/ips" ^| head -10

pause

# EdgeOne UFW 定时任务配置指南

## 概述

本指南说明如何在各种系统中设置EdgeOne UFW更新器的定时任务，包括宝塔面板、原生crontab等。

## 宝塔面板配置

### 1. 脚本部署

将脚本上传到服务器，建议路径：
```
/www/server/scripts/update_edgeone_ufw.sh
```

设置执行权限：
```bash
chmod +x /www/server/scripts/update_edgeone_ufw.sh
```

### 2. 宝塔面板定时任务设置

1. 登录宝塔面板
2. 进入 "计划任务" 
3. 点击 "添加任务"
4. 配置如下：

**任务类型**: Shell脚本
**任务名称**: EdgeOne UFW规则更新
**执行周期**: 每天 02:00
**脚本内容**:
```bash
#!/bin/bash
/www/server/scripts/update_edgeone_ufw.sh -v >> /var/log/edgeone_cron.log 2>&1
```

### 3. 高级配置选项

#### 选项1: 使用自定义端口
```bash
#!/bin/bash
/www/server/scripts/update_edgeone_ufw.sh -p "53717,80,443" -v >> /var/log/edgeone_cron.log 2>&1
```

#### 选项2: 带错误处理
```bash
#!/bin/bash
LOG_FILE="/var/log/edgeone_cron.log"
SCRIPT_PATH="/www/server/scripts/update_edgeone_ufw.sh"

echo "[$(date)] 开始执行EdgeOne UFW更新" >> "$LOG_FILE"

if [ -f "$SCRIPT_PATH" ]; then
    "$SCRIPT_PATH" -v >> "$LOG_FILE" 2>&1
    if [ $? -eq 0 ]; then
        echo "[$(date)] EdgeOne UFW更新成功" >> "$LOG_FILE"
    else
        echo "[$(date)] EdgeOne UFW更新失败" >> "$LOG_FILE"
    fi
else
    echo "[$(date)] 错误: 脚本文件不存在: $SCRIPT_PATH" >> "$LOG_FILE"
fi
```

## 原生Crontab配置

### 1. 编辑crontab
```bash
sudo crontab -e
```

### 2. 添加定时任务

#### 基本配置（每天凌晨2点执行）
```bash
0 2 * * * /usr/local/bin/update_edgeone_ufw.sh -v >> /var/log/edgeone_ufw_update.log 2>&1
```

#### 高频更新（每6小时执行一次）
```bash
0 */6 * * * /usr/local/bin/update_edgeone_ufw.sh -v >> /var/log/edgeone_ufw_update.log 2>&1
```

#### 带随机延迟（避免同时请求API）
```bash
0 2 * * * sleep $((RANDOM \% 1800)) && /usr/local/bin/update_edgeone_ufw.sh -v >> /var/log/edgeone_ufw_update.log 2>&1
```

## Systemd Timer配置（推荐）

如果您已经按照主安装脚本安装，systemd timer已经配置好了：

### 查看状态
```bash
sudo systemctl status edgeone-ufw-update.timer
```

### 手动触发
```bash
sudo systemctl start edgeone-ufw-update.service
```

### 查看下次执行时间
```bash
sudo systemctl list-timers edgeone-ufw-update.timer
```

## 日志管理

### 1. 日志文件位置
- 主日志: `/var/log/edgeone_ufw_update.log`
- 定时任务日志: `/var/log/edgeone_cron.log`
- 系统日志: `journalctl -u edgeone-ufw-update.service`

### 2. 日志轮转配置

创建 `/etc/logrotate.d/edgeone-cron`:
```
/var/log/edgeone_cron.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
```

### 3. 日志监控命令
```bash
# 实时查看日志
tail -f /var/log/edgeone_ufw_update.log

# 查看最近的执行记录
tail -50 /var/log/edgeone_cron.log

# 查看错误信息
grep -i error /var/log/edgeone_ufw_update.log
```

## 故障排除

### 1. 常见问题

#### 问题: "未知参数: start"
**原因**: 某些定时任务系统会传入额外参数
**解决**: 脚本已更新支持忽略这些参数

#### 问题: 权限不足
**原因**: 脚本需要root权限修改UFW规则
**解决**: 确保定时任务以root身份运行

#### 问题: 网络连接失败
**原因**: 无法访问EdgeOne API
**解决**: 检查网络连接和防火墙设置

### 2. 测试定时任务

#### 手动测试脚本
```bash
sudo /www/server/scripts/update_edgeone_ufw.sh --dry-run -v
```

#### 测试定时任务命令
```bash
# 模拟cron环境
sudo env -i PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin /www/server/scripts/update_edgeone_ufw.sh --dry-run
```

### 3. 监控脚本

创建监控脚本 `/www/server/scripts/check_edgeone_status.sh`:
```bash
#!/bin/bash

LOG_FILE="/var/log/edgeone_ufw_update.log"
ALERT_EMAIL="<EMAIL>"

# 检查最近24小时是否有更新记录
if ! grep -q "$(date -d '1 day ago' '+%Y-%m-%d')" "$LOG_FILE"; then
    echo "警告: EdgeOne UFW更新器超过24小时未运行" | mail -s "EdgeOne UFW Alert" "$ALERT_EMAIL"
fi

# 检查UFW规则数量
RULE_COUNT=$(ufw status | grep -c "EdgeOne" || echo 0)
if [ "$RULE_COUNT" -eq 0 ]; then
    echo "警告: 没有发现EdgeOne UFW规则" | mail -s "EdgeOne UFW Alert" "$ALERT_EMAIL"
fi
```

## 性能优化

### 1. 减少API请求频率
- 建议每天更新1-2次即可
- 避免在高峰时段执行
- 使用随机延迟分散请求时间

### 2. 优化脚本执行
```bash
# 使用ionice降低IO优先级
ionice -c 3 /www/server/scripts/update_edgeone_ufw.sh

# 使用nice降低CPU优先级  
nice -n 10 /www/server/scripts/update_edgeone_ufw.sh
```

### 3. 批量处理
对于大量IP的情况，可以考虑分批处理：
```bash
# 每次只处理前100个IP（需要修改脚本）
/www/server/scripts/update_edgeone_ufw.sh --batch-size 100
```

## 安全建议

1. **最小权限**: 只给定时任务必要的权限
2. **日志审计**: 定期检查执行日志
3. **备份规则**: 定期备份UFW配置
4. **监控告警**: 设置执行失败告警
5. **网络安全**: 确保API请求的安全性

## 示例配置文件

完整的宝塔面板定时任务配置示例：

```bash
#!/bin/bash
# EdgeOne UFW自动更新脚本
# 执行时间: 每天凌晨2:30
# 作者: EdgeOne UFW Updater

# 配置变量
SCRIPT_PATH="/www/server/scripts/update_edgeone_ufw.sh"
LOG_FILE="/var/log/edgeone_cron.log"
PORTS="53717"  # 自定义端口

# 检查脚本是否存在
if [ ! -f "$SCRIPT_PATH" ]; then
    echo "[$(date)] 错误: 脚本文件不存在: $SCRIPT_PATH" >> "$LOG_FILE"
    exit 1
fi

# 执行更新
echo "[$(date)] 开始执行EdgeOne UFW更新，端口: $PORTS" >> "$LOG_FILE"

"$SCRIPT_PATH" -p "$PORTS" -v >> "$LOG_FILE" 2>&1

if [ $? -eq 0 ]; then
    echo "[$(date)] EdgeOne UFW更新成功完成" >> "$LOG_FILE"
else
    echo "[$(date)] EdgeOne UFW更新执行失败" >> "$LOG_FILE"
fi

# 清理旧日志（保留最近30天）
find /var/log -name "edgeone_*.log" -mtime +30 -delete 2>/dev/null

echo "[$(date)] 定时任务执行完成" >> "$LOG_FILE"
```

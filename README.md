# EdgeOne UFW 自动更新器

自动获取腾讯云EdgeOne的IPv4和IPv6地址列表，并智能更新UFW防火墙规则的工具。

## 功能特性

- 🔄 **自动更新**: 定时获取最新的EdgeOne IP列表
- 🧠 **智能对比**: 只添加新IP，删除过时IP，避免重复规则
- 🌐 **双栈支持**: 同时支持IPv4和IPv6地址
- 🔧 **灵活配置**: 可自定义端口、更新频率等
- 📝 **详细日志**: 完整的操作日志记录
- 🛡️ **安全设计**: 最小权限原则，安全的systemd配置
- 🎯 **预览模式**: dry-run模式预览操作而不实际执行

## 快速开始

### 1. 下载和安装

```bash
# 克隆或下载项目文件
git clone <repository-url>
cd edgeone-ufw-updater

# 或者直接下载文件
wget https://api.edgeone.ai/ips  # 测试API可用性

# 给安装脚本执行权限
chmod +x install.sh

# 运行安装脚本
sudo ./install.sh
```

### 2. 基本使用

```bash
# 手动更新规则（使用默认端口80,443,8080,8443）
sudo /usr/local/bin/update_edgeone_ufw.sh

# 交互式模式（提示用户输入端口）
sudo /usr/local/bin/update_edgeone_ufw.sh -i

# 预览模式（不实际修改规则）
sudo /usr/local/bin/update_edgeone_ufw.sh --dry-run

# 指定自定义端口
sudo /usr/local/bin/update_edgeone_ufw.sh -p "80,443,22,8080"

# 详细输出模式
sudo /usr/local/bin/update_edgeone_ufw.sh -v

# 删除所有EdgeOne相关规则
sudo /usr/local/bin/update_edgeone_ufw.sh --remove-all
```

## 详细说明

### 交互式端口配置

使用 `-i` 或 `--interactive` 参数可以启动交互式模式，脚本会提示您输入要配置的端口：

```bash
sudo /usr/local/bin/update_edgeone_ufw.sh -i
```

交互式模式示例：
```
=== 交互式端口配置 ===
当前默认端口: 80,443,8080,8443

常用端口参考:
  Web服务: 80,443
  SSH: 22
  FTP: 21,22
  邮件: 25,110,143,993,995
  数据库: 3306,5432,1433,27017
  自定义Web: 8080,8443,9000

请输入要配置的端口列表（用逗号分隔，回车使用默认）: 80,443,22
已设置端口: 80,443,22

是否继续执行更新？(y/N): y
```

### 脚本参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `-p, --ports` | 指定端口列表（逗号分隔） | `-p "80,443,22"` |
| `-i, --interactive` | 交互式模式，提示用户输入端口 | `-i` |
| `-d, --dry-run` | 预览模式，不实际修改规则 | `--dry-run` |
| `-v, --verbose` | 详细输出模式 | `-v` |
| `--remove-all` | 删除所有EdgeOne规则 | `--remove-all` |
| `-h, --help` | 显示帮助信息 | `-h` |

### 自动化配置

安装后会自动配置systemd定时器，默认每天凌晨2点执行更新：

```bash
# 查看定时器状态
sudo systemctl status edgeone-ufw-update.timer

# 查看下次执行时间
sudo systemctl list-timers edgeone-ufw-update.timer

# 手动触发一次更新
sudo systemctl start edgeone-ufw-update.service

# 查看服务日志
sudo journalctl -u edgeone-ufw-update.service -f
```

### 日志管理

```bash
# 查看更新日志
sudo tail -f /var/log/edgeone_ufw_update.log

# 查看systemd服务日志
sudo journalctl -u edgeone-ufw-update.service

# 日志会自动轮转，保留30天
```

## 配置文件

### 主配置文件: `/etc/edgeone-ufw-updater/config.conf`

```bash
# API URL
API_URL="https://api.edgeone.ai/ips"

# 默认端口列表
DEFAULT_PORTS="80,443,8080,8443"

# 日志文件路径
LOG_FILE="/var/log/edgeone_ufw_update.log"

# UFW规则前缀
RULE_PREFIX="EdgeOne"
```

### 定时器配置: `/etc/systemd/system/edgeone-ufw-update.timer`

```ini
[Timer]
# 每天凌晨2点执行
OnCalendar=*-*-* 02:00:00
# 随机延迟0-30分钟
RandomizedDelaySec=1800
# 开机5分钟后执行一次
OnBootSec=5min
```

## 工作原理

1. **获取IP列表**: 从EdgeOne API获取最新的IPv4和IPv6地址段
2. **智能对比**: 与上次获取的列表对比，识别新增和删除的IP段
3. **规则管理**: 
   - 删除过时的UFW规则
   - 为新IP段添加UFW规则
   - 支持多个端口的批量配置
4. **日志记录**: 记录所有操作和变更

## 安全考虑

- 脚本需要root权限来修改UFW规则
- systemd服务配置了安全限制（NoNewPrivileges, ProtectSystem等）
- 临时文件存储在受保护的目录
- 支持dry-run模式预览操作

## 故障排除

### 常见问题

1. **API无法访问**
   ```bash
   # 测试API连接
   curl -s https://api.edgeone.ai/ips | head -5
   ```

2. **权限问题**
   ```bash
   # 确保以root权限运行
   sudo /usr/local/bin/update_edgeone_ufw.sh
   ```

3. **UFW未启用**
   ```bash
   # 启用UFW
   sudo ufw enable
   ```

4. **查看详细错误**
   ```bash
   # 使用详细模式
   sudo /usr/local/bin/update_edgeone_ufw.sh -v
   
   # 查看系统日志
   sudo journalctl -u edgeone-ufw-update.service -n 50
   ```

### 手动清理

如果需要手动清理所有EdgeOne规则：

```bash
# 删除所有EdgeOne相关规则
sudo /usr/local/bin/update_edgeone_ufw.sh --remove-all

# 或者手动删除（谨慎操作）
sudo ufw status numbered | grep EdgeOne
# 然后根据编号删除规则
sudo ufw delete [规则编号]
```

## 卸载

```bash
# 运行卸载脚本
sudo ./install.sh uninstall

# 手动清理残留规则（可选）
sudo /usr/local/bin/update_edgeone_ufw.sh --remove-all
```

## 自定义和扩展

### 修改更新频率

编辑定时器配置：
```bash
sudo systemctl edit edgeone-ufw-update.timer
```

添加覆盖配置：
```ini
[Timer]
OnCalendar=*-*-* 06:00:00  # 改为每天6点
RandomizedDelaySec=600     # 随机延迟10分钟
```

### 添加自定义端口

```bash
# 临时指定端口
sudo /usr/local/bin/update_edgeone_ufw.sh -p "80,443,22,3389,5432"

# 永久修改默认端口
sudo nano /etc/edgeone-ufw-updater/config.conf
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

- v1.0.0: 初始版本，支持IPv4/IPv6自动更新
- 支持智能对比和增量更新
- 完整的systemd集成和日志管理

# EdgeOne iptables 版本使用指南

## 概述

EdgeOne iptables 版本直接使用 Linux 内核的 iptables 防火墙，提供更底层的控制和更好的性能。适合所有 Linux 发行版，特别是服务器环境。

## 主要特性

- 🔥 **直接 iptables 控制**：无需额外的防火墙管理工具
- 🎯 **自定义链管理**：创建专门的 EdgeOne 规则链，便于管理
- 🌐 **IPv4/IPv6 双栈支持**：同时支持 IPv4 和 IPv6 规则
- ⚡ **高性能**：直接操作内核防火墙，性能最优
- 🔧 **灵活配置**：支持自定义链名称和端口配置

## 快速开始

### 1. 安装

```bash
# 自动选择防火墙类型
sudo ./install.sh

# 或直接安装 iptables 版本
sudo ./install.sh install-iptables
```

### 2. 基本使用

```bash
# 使用默认端口（80,443,8080,8443）
sudo ./update_edgeone_iptables.sh

# 预览模式（推荐首次使用）
sudo ./update_edgeone_iptables.sh --dry-run

# 指定自定义端口
sudo ./update_edgeone_iptables.sh -p "53717"

# 交互式端口配置
sudo ./update_edgeone_iptables.sh -i

# 详细输出模式
sudo ./update_edgeone_iptables.sh -v

# 删除所有 EdgeOne 规则
sudo ./update_edgeone_iptables.sh --remove-all
```

## 工作原理

### 1. 自定义链结构

脚本创建两个自定义链：
- `EDGEONE_ALLOW`：处理 IPv4 规则
- `EDGEONE_ALLOW_V6`：处理 IPv6 规则

### 2. 规则组织

```
INPUT 链
├── EDGEONE_ALLOW (IPv4)
│   ├── -s *******/24 -p tcp --dport 53717 -j ACCEPT
│   ├── -s *******/24 -p tcp --dport 53717 -j ACCEPT
│   └── ...
└── EDGEONE_ALLOW_V6 (IPv6)
    ├── -s 2001:db8::/32 -p tcp --dport 53717 -j ACCEPT
    └── ...
```

### 3. 更新流程

1. 获取最新的 EdgeOne IP 列表
2. 清空现有的自定义链规则
3. 添加新的规则到自定义链
4. 确保自定义链链接到 INPUT 链

## 详细参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `-p, --ports` | 指定端口列表（逗号分隔） | `-p "53717,80,443"` |
| `-i, --interactive` | 交互式端口配置 | `-i` |
| `-d, --dry-run` | 预览模式，不实际修改规则 | `--dry-run` |
| `-v, --verbose` | 详细输出模式 | `-v` |
| `--remove-all` | 删除所有 EdgeOne 规则 | `--remove-all` |
| `--chain-name` | 自定义链名称 | `--chain-name CUSTOM_CHAIN` |

## 高级配置

### 1. 自定义链名称

```bash
# 使用自定义链名称
sudo ./update_edgeone_iptables.sh --chain-name MY_EDGEONE_CHAIN -p "53717"
```

### 2. 规则持久化

#### Ubuntu/Debian 系统
```bash
# 安装 iptables-persistent
sudo apt install iptables-persistent

# 保存当前规则
sudo iptables-save > /etc/iptables/rules.v4
sudo ip6tables-save > /etc/iptables/rules.v6
```

#### CentOS/RHEL 系统
```bash
# 安装 iptables-services
sudo yum install iptables-services

# 启用服务
sudo systemctl enable iptables
sudo systemctl enable ip6tables

# 保存规则
sudo service iptables save
sudo service ip6tables save
```

### 3. 定时任务配置

脚本会自动配置 systemd 定时器：

```bash
# 查看定时器状态
sudo systemctl status edgeone-iptables-update.timer

# 手动触发更新
sudo systemctl start edgeone-iptables-update.service

# 查看日志
sudo journalctl -u edgeone-iptables-update.service -f
```

## 管理和维护

### 1. 查看当前规则

```bash
# 查看 IPv4 规则
sudo iptables -L EDGEONE_ALLOW -n --line-numbers

# 查看 IPv6 规则
sudo ip6tables -L EDGEONE_ALLOW_V6 -n --line-numbers

# 查看所有规则
sudo iptables -L -n | grep -A 20 EDGEONE
```

### 2. 手动管理规则

```bash
# 手动添加单个规则
sudo iptables -A EDGEONE_ALLOW -s *******/32 -p tcp --dport 53717 -j ACCEPT

# 手动删除规则（按行号）
sudo iptables -D EDGEONE_ALLOW 1

# 清空所有 EdgeOne 规则
sudo iptables -F EDGEONE_ALLOW
sudo ip6tables -F EDGEONE_ALLOW_V6
```

### 3. 备份和恢复

```bash
# 备份当前规则
sudo iptables-save > edgeone_iptables_backup.txt

# 恢复规则
sudo iptables-restore < edgeone_iptables_backup.txt
```

## 故障排除

### 1. 常见问题

#### 问题：规则添加失败
```bash
# 检查 iptables 状态
sudo iptables -L

# 检查内核模块
lsmod | grep ip_tables

# 重新加载模块
sudo modprobe ip_tables
```

#### 问题：IPv6 规则不工作
```bash
# 检查 IPv6 支持
cat /proc/net/if_inet6

# 检查 ip6tables
sudo ip6tables -L
```

#### 问题：规则重启后丢失
```bash
# 安装持久化工具
sudo apt install iptables-persistent  # Ubuntu/Debian
sudo yum install iptables-services    # CentOS/RHEL

# 保存规则
sudo iptables-save > /etc/iptables/rules.v4
```

### 2. 调试命令

```bash
# 测试脚本
sudo ./test_iptables.sh

# 详细模式运行
sudo ./update_edgeone_iptables.sh -v --dry-run

# 查看详细日志
sudo tail -f /var/log/edgeone_iptables_update.log
```

## 性能优化

### 1. 规则优化

- 将最常用的 IP 段放在链的前面
- 使用 CIDR 表示法合并相邻的 IP 段
- 定期清理不需要的规则

### 2. 系统优化

```bash
# 增加 iptables 哈希表大小
echo 'net.netfilter.nf_conntrack_buckets = 65536' >> /etc/sysctl.conf

# 增加连接跟踪表大小
echo 'net.netfilter.nf_conntrack_max = 262144' >> /etc/sysctl.conf

# 应用设置
sudo sysctl -p
```

## 与 UFW 版本的对比

| 特性 | iptables 版本 | UFW 版本 |
|------|---------------|----------|
| 性能 | 更高 | 较高 |
| 复杂度 | 较复杂 | 简单 |
| 兼容性 | 所有 Linux | 主要是 Ubuntu |
| 控制粒度 | 精细 | 基本 |
| 学习曲线 | 陡峭 | 平缓 |

## 安全建议

1. **测试环境**：先在测试环境验证规则
2. **备份规则**：定期备份 iptables 规则
3. **监控日志**：定期检查防火墙日志
4. **最小权限**：只开放必要的端口
5. **定期更新**：保持 EdgeOne IP 列表最新

## 示例配置

### 完整的生产环境配置

```bash
#!/bin/bash
# 生产环境 EdgeOne iptables 配置示例

# 1. 更新 EdgeOne 规则（端口 53717）
sudo ./update_edgeone_iptables.sh -p "53717" -v

# 2. 保存规则
sudo iptables-save > /etc/iptables/rules.v4
sudo ip6tables-save > /etc/iptables/rules.v6

# 3. 验证规则
echo "当前 EdgeOne 规则数量:"
sudo iptables -L EDGEONE_ALLOW --line-numbers | grep -c ACCEPT

# 4. 显示前 5 条规则
echo "前 5 条 EdgeOne 规则:"
sudo iptables -L EDGEONE_ALLOW -n | head -10
```

这个 iptables 版本提供了更直接、更高性能的防火墙管理方式，适合对性能和控制精度有更高要求的环境。
